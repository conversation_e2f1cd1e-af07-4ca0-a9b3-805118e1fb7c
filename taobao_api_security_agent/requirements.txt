# LangGraph and LangChain dependencies
langgraph>=0.2.0
langchain>=0.3.0
langchain-core>=0.3.0
langchain-community>=0.3.0

# LLM providers
langchain-anthropic>=0.2.0
langchain-google-genai>=2.0.0
langchain-openai>=0.2.0

# HTTP and networking
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.9.0

# Data processing
pydantic>=2.5.0
pydantic-settings>=2.1.0
PyYAML>=6.0.1
python-dotenv>=1.0.0

# Security and cryptography
cryptography>=41.0.0
hashlib-compat>=1.0.0

# Web scraping and parsing
beautifulsoup4>=4.12.0
lxml>=4.9.0
html5lib>=1.1

# Data analysis
pandas>=2.1.0
numpy>=1.24.0

# Fuzzing and testing
hypothesis>=6.88.0
faker>=20.0.0

# Logging and monitoring
structlog>=23.2.0
rich>=13.7.0

# Configuration and utilities
click>=8.1.0
typer>=0.9.0
python-multipart>=0.0.6

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0

# Optional: Database support for persistence
sqlalchemy>=2.0.0
alembic>=1.12.0

# Optional: Redis for caching
redis>=5.0.0

# Optional: Monitoring and metrics
prometheus-client>=0.18.0
