# 淘宝API安全检测Agent

基于LangGraph框架构建的专业API安全检测Agent，专门用于检测淘宝网站及其子域名系统的API安全问题。

## 功能特性

### 核心安全检测能力
- **字段过度透出检测**: 识别API响应中可能泄露敏感信息的字段
- **越权访问检测**: 检测API是否存在权限控制缺陷
- **反爬策略检测**: 评估API的反爬虫防护措施
- **OWASP API Security Top 10**: 基于OWASP标准的全面安全漏洞检测

### 多样化输入支持
- 原始HTTP流量数据解析
- API接口列表批量检测
- 网页爬虫数据分析
- 支持淘宝主站及所有子域名

### 智能化分析
- 集成多种大模型：Gemini Pro 2.5、Claude、Qwen
- 自动化安全风险评估
- 智能化漏洞分类和优先级排序

### 淘宝特化功能
- 淘宝请求签名服务集成
- 登录态自动管理
- 子域名系统适配

## 项目结构

```
taobao_api_security_agent/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖
├── config/                      # 配置文件目录
│   ├── agent_config.yaml       # Agent主配置
│   ├── llm_config.yaml         # 大模型配置
│   ├── signature_config.yaml   # 签名服务配置
│   └── session_config.yaml     # 会话配置
├── src/                         # 源代码目录
│   ├── __init__.py
│   ├── agent/                   # Agent核心模块
│   │   ├── __init__.py
│   │   ├── main_agent.py       # 主Agent工作流
│   │   └── state.py            # Agent状态定义
│   ├── parsers/                 # 输入解析模块
│   │   ├── __init__.py
│   │   ├── traffic_parser.py   # 流量数据解析
│   │   ├── api_list_parser.py  # API列表解析
│   │   └── web_data_parser.py  # 网页数据解析
│   ├── security/                # 安全检测模块
│   │   ├── __init__.py
│   │   ├── field_exposure.py   # 字段过度透出检测
│   │   ├── privilege_escalation.py # 越权检测
│   │   ├── anti_crawler.py     # 反爬检测
│   │   └── owasp_fuzzer.py     # OWASP Fuzz测试
│   ├── integrations/            # 外部集成模块
│   │   ├── __init__.py
│   │   ├── signature_service.py # 签名服务集成
│   │   ├── session_manager.py  # 会话管理
│   │   └── llm_provider.py     # LLM提供商集成
│   ├── tools/                   # 工具模块
│   │   ├── __init__.py
│   │   ├── request_builder.py  # 请求构建工具
│   │   ├── response_analyzer.py # 响应分析工具
│   │   └── report_generator.py # 报告生成工具
│   └── utils/                   # 工具函数
│       ├── __init__.py
│       ├── config_loader.py    # 配置加载器
│       ├── logger.py           # 日志工具
│       └── validators.py       # 数据验证器
├── tests/                       # 测试目录
│   ├── __init__.py
│   ├── test_parsers/
│   ├── test_security/
│   ├── test_integrations/
│   └── test_tools/
├── examples/                    # 示例和文档
│   ├── sample_configs/         # 示例配置
│   ├── sample_data/            # 示例数据
│   └── usage_examples.py       # 使用示例
└── docs/                        # 详细文档
    ├── installation.md         # 安装指南
    ├── configuration.md        # 配置说明
    ├── api_reference.md        # API参考
    └── security_checks.md      # 安全检查说明
```

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置设置
1. 复制示例配置文件到config目录
2. 根据实际环境修改配置参数
3. 设置签名服务和会话信息

### 运行示例
```python
from src.agent.main_agent import TaobaoAPISecurityAgent

# 初始化Agent
agent = TaobaoAPISecurityAgent()

# 检测API列表
results = agent.analyze_api_list([
    "https://main.m.taobao.com/api/user/info",
    "https://www.taobao.com/api/product/search"
])

# 生成报告
agent.generate_report(results)
```

## 配置说明

详细的配置说明请参考 [docs/configuration.md](docs/configuration.md)

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。
