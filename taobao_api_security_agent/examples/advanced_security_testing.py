"""
高级安全测试示例

展示如何使用淘宝API安全检测Agent进行深度安全测试。
"""

import json
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.main_agent import TaobaoAPISecurityAgent
from src.agent.state import InputType
from src.security.request_replayer import RequestReplayer
from src.security.response_analyzer import ResponseAnalyzer
from src.security.parameter_fuzzer import ParameterFuzzer
from src.security.field_exposure import FieldExposureDetector
from src.security.privilege_escalation import PrivilegeEscalationDetector
from src.security.anti_crawler import AntiCrawlerDetector


async def example_comprehensive_security_test():
    """示例：全面的安全测试"""
    print("=== 全面安全测试示例 ===")
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    # 定义测试API
    test_apis = [
        {
            "url": "https://main.m.taobao.com/api/user/profile",
            "method": "GET",
            "description": "用户资料接口",
            "request_params": {"userId": "12345", "format": "json"}
        },
        {
            "url": "https://www.taobao.com/api/admin/users",
            "method": "GET", 
            "description": "管理员用户接口",
            "request_params": {"page": "1", "limit": "10"}
        },
        {
            "url": "https://cart.taobao.com/api/cart/items",
            "method": "GET",
            "description": "购物车接口",
            "request_params": {"cartId": "cart123"}
        }
    ]
    
    # 自定义安全检测配置
    security_config = {
        "security_checks": {
            "field_exposure": {
                "enabled": True,
                "max_test_iterations": 15,
                "test_different_users": True,
                "test_parameter_manipulation": True
            },
            "privilege_escalation": {
                "enabled": True,
                "test_horizontal_escalation": True,
                "test_vertical_escalation": True,
                "max_user_id_tests": 20
            },
            "anti_crawler": {
                "enabled": True,
                "rate_limit_test_count": 30,
                "user_agent_test_enabled": True,
                "captcha_detection_enabled": True,
                "behavior_analysis_enabled": True
            }
        },
        "analysis_mode": "comprehensive"
    }
    
    try:
        # 执行全面安全分析
        result = agent.analyze(
            json.dumps(test_apis),
            InputType.API_LIST,
            security_config
        )
        
        # 输出详细结果
        print(f"安全测试完成！")
        print(f"总API数量: {result['final_report']['summary']['total_apis']}")
        print(f"发现漏洞数量: {result['final_report']['summary']['total_vulnerabilities']}")
        print(f"分析时间: {result['final_report']['summary']['analysis_time']}")
        
        # 按风险等级分类显示漏洞
        if result['security_results']:
            print("\n=== 发现的安全问题 ===")
            
            # 按风险等级分组
            risk_groups = {}
            for security_result in result['security_results']:
                risk_level = security_result.risk_level
                if risk_level not in risk_groups:
                    risk_groups[risk_level] = []
                risk_groups[risk_level].append(security_result)
            
            # 按优先级显示
            for risk_level in ['critical', 'high', 'medium', 'low', 'info']:
                if risk_level in risk_groups:
                    print(f"\n{risk_level.upper()} 风险 ({len(risk_groups[risk_level])} 个):")
                    for i, result in enumerate(risk_groups[risk_level], 1):
                        print(f"  {i}. {result.title}")
                        print(f"     API: {result.api_info.url}")
                        print(f"     类型: {result.check_type}")
                        print(f"     描述: {result.description}")
                        if result.evidence:
                            print(f"     证据: {', '.join(result.evidence[:2])}")
                        print()
        else:
            print("未发现明显的安全问题")
            
    except Exception as e:
        print(f"安全测试失败: {e}")
        import traceback
        traceback.print_exc()


async def example_field_exposure_detection():
    """示例：字段过度透出检测"""
    print("=== 字段过度透出检测示例 ===")
    
    # 模拟包含敏感信息的API响应
    sensitive_api_data = {
        "url": "https://main.m.taobao.com/api/user/detail",
        "method": "GET",
        "request_params": {"userId": "12345"},
        "response_body": json.dumps({
            "code": 200,
            "data": {
                "userId": "12345",
                "username": "testuser",
                "realName": "张三",
                "phone": "***********",
                "email": "<EMAIL>",
                "idCard": "110101199001011234",
                "bankCard": "6222021234567890123",
                "address": "北京市朝阳区某某街道123号",
                "balance": 1000.50,
                "vipLevel": 3,
                "lastLoginTime": "2024-01-01 10:00:00",
                "createTime": "2023-01-01 00:00:00"
            }
        })
    }
    
    try:
        # 初始化响应分析器
        response_analyzer = ResponseAnalyzer({
            'sensitive_keywords': [
                'phone', 'email', 'idcard', 'bankcard', 'address', 
                'realname', 'password', 'token'
            ],
            'max_depth': 5
        })
        
        # 创建API信息对象
        from src.agent.state import APIInfo
        from datetime import datetime
        from urllib.parse import urlparse
        
        parsed_url = urlparse(sensitive_api_data['url'])
        api_info = APIInfo(
            url=sensitive_api_data['url'],
            method=sensitive_api_data['method'],
            domain=parsed_url.netloc,
            path=parsed_url.path,
            request_params=sensitive_api_data['request_params'],
            response_body=sensitive_api_data['response_body'],
            timestamp=datetime.now(),
            source='test'
        )
        
        # 分析响应
        response_data = {
            'body': sensitive_api_data['response_body'],
            'status_code': 200,
            'headers': {'content-type': 'application/json'}
        }
        
        results = response_analyzer.analyze_response(response_data, api_info)
        
        print(f"字段过度透出检测完成，发现 {len(results)} 个问题：")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.title}")
            print(f"   风险等级: {result.risk_level}")
            print(f"   描述: {result.description}")
            if result.evidence:
                print(f"   证据: {', '.join(result.evidence)}")
            print(f"   建议: {result.recommendation}")
            print()
            
    except Exception as e:
        print(f"字段过度透出检测失败: {e}")
        import traceback
        traceback.print_exc()


async def example_parameter_fuzzing():
    """示例：参数Fuzz测试"""
    print("=== 参数Fuzz测试示例 ===")
    
    try:
        # 初始化参数Fuzz测试器
        parameter_fuzzer = ParameterFuzzer({
            'max_fuzz_iterations': 20,
            'fuzz_string_length': 100
        })
        
        # 原始参数
        original_params = {
            'userId': '12345',
            'page': '1',
            'limit': '10',
            'format': 'json'
        }
        
        print("生成Fuzz测试用例...")
        
        # 生成Fuzz测试参数
        fuzz_count = 0
        for fuzz_type, fuzz_params in parameter_fuzzer.generate_fuzz_parameters(
            original_params, 
            ['sql_injection', 'xss', 'boundary_values', 'type_confusion']
        ):
            fuzz_count += 1
            if fuzz_count <= 10:  # 只显示前10个示例
                print(f"{fuzz_count}. {fuzz_type}:")
                print(f"   原始参数: {original_params}")
                print(f"   Fuzz参数: {fuzz_params}")
                print()
            
            if fuzz_count >= 20:  # 限制总数
                break
        
        print(f"总共生成了 {fuzz_count} 个Fuzz测试用例")
        
        # 模拟分析Fuzz响应
        print("\n模拟Fuzz响应分析...")
        
        # 创建模拟的API信息
        from src.agent.state import APIInfo
        from datetime import datetime
        
        api_info = APIInfo(
            url="https://example.com/api/test",
            method="GET",
            domain="example.com",
            path="/api/test",
            timestamp=datetime.now(),
            source='test'
        )
        
        # 模拟原始响应和Fuzz响应
        original_response = {
            'status_code': 200,
            'body': '{"code": 200, "data": "normal response"}',
            'response_time': 0.5
        }
        
        fuzz_response = {
            'status_code': 500,
            'body': 'SQL syntax error: You have an error in your SQL syntax',
            'response_time': 2.0
        }
        
        # 分析Fuzz响应
        fuzz_results = parameter_fuzzer.analyze_fuzz_response(
            original_response, fuzz_response, 'sql_injection', api_info
        )
        
        if fuzz_results:
            print("Fuzz测试发现的问题:")
            for result in fuzz_results:
                print(f"- {result.title}")
                print(f"  风险等级: {result.risk_level}")
                print(f"  描述: {result.description}")
                print()
        else:
            print("Fuzz测试未发现明显问题")
            
    except Exception as e:
        print(f"参数Fuzz测试失败: {e}")
        import traceback
        traceback.print_exc()


async def example_privilege_escalation_test():
    """示例：越权访问测试"""
    print("=== 越权访问测试示例 ===")
    
    try:
        # 模拟不同类型的用户会话
        from src.agent.state import SessionInfo
        from datetime import datetime, timedelta
        
        sessions = [
            SessionInfo(
                user_id="normal_user",
                user_type="normal",
                cookies={"session": "normal_session_token"},
                headers={"User-Agent": "Normal User Browser"},
                expires_at=datetime.now() + timedelta(hours=1),
                is_valid=True
            ),
            SessionInfo(
                user_id="vip_user", 
                user_type="vip",
                cookies={"session": "vip_session_token"},
                headers={"User-Agent": "VIP User Browser"},
                expires_at=datetime.now() + timedelta(hours=1),
                is_valid=True
            ),
            SessionInfo(
                user_id="admin_user",
                user_type="admin", 
                cookies={"session": "admin_session_token"},
                headers={"User-Agent": "Admin Browser"},
                expires_at=datetime.now() + timedelta(hours=1),
                is_valid=True
            )
        ]
        
        print("模拟用户会话:")
        for session in sessions:
            print(f"- {session.user_id} ({session.user_type})")
        
        print("\n越权测试场景:")
        print("1. 水平越权: 普通用户尝试访问其他用户数据")
        print("2. 垂直越权: 普通用户尝试访问管理员功能")
        print("3. 参数篡改: 修改用户ID参数")
        print("4. 权限提升: 添加admin参数")
        
        # 这里在实际环境中会执行真实的越权测试
        print("\n注意: 实际测试需要真实的API端点和有效的会话信息")
        
    except Exception as e:
        print(f"越权访问测试失败: {e}")


async def main():
    """主函数"""
    print("淘宝API安全检测Agent - 高级安全测试示例")
    print("=" * 60)
    
    try:
        # 运行各种高级安全测试示例
        await example_field_exposure_detection()
        print()
        
        await example_parameter_fuzzing()
        print()
        
        await example_privilege_escalation_test()
        print()
        
        # 注意：全面安全测试需要真实的API端点
        print("注意：全面安全测试需要配置真实的API端点和会话信息")
        print("请参考配置文件示例进行设置")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
