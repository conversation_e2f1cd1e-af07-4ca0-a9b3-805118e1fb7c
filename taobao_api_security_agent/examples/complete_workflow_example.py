"""
完整工作流示例

展示淘宝API安全检测Agent的完整工作流程，包括所有功能模块。
"""

import json
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.main_agent import TaobaoAPISecurityAgent
from src.agent.state import InputType


async def complete_workflow_example():
    """完整工作流示例"""
    print("=== 淘宝API安全检测Agent - 完整工作流 ===")
    print()
    
    # 1. 准备测试数据
    print("1. 准备测试数据")
    print("-" * 30)
    
    # 模拟真实的淘宝API列表
    taobao_apis = [
        {
            "url": "https://main.m.taobao.com/api/user/profile",
            "method": "GET",
            "description": "用户资料接口",
            "request_params": {"userId": "12345", "format": "json"},
            "request_headers": {
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
                "Cookie": "_tb_token_=abc123; cookie2=def456"
            }
        },
        {
            "url": "https://www.taobao.com/api/product/search",
            "method": "GET",
            "description": "商品搜索接口",
            "request_params": {"q": "手机", "page": "1", "size": "20"},
            "request_headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
                "Referer": "https://www.taobao.com"
            }
        },
        {
            "url": "https://cart.taobao.com/api/cart/add",
            "method": "POST",
            "description": "添加购物车接口",
            "request_params": {"itemId": "67890", "quantity": "1"},
            "request_headers": {
                "Content-Type": "application/json",
                "Cookie": "_tb_token_=abc123"
            }
        },
        {
            "url": "https://trade.taobao.com/api/order/create",
            "method": "POST",
            "description": "创建订单接口",
            "request_params": {"cartId": "cart123", "addressId": "addr456"},
            "request_headers": {
                "Content-Type": "application/json",
                "Cookie": "_tb_token_=abc123; login=true"
            }
        },
        {
            "url": "https://www.taobao.com/api/admin/users",
            "method": "GET",
            "description": "管理员用户接口（测试越权）",
            "request_params": {"page": "1", "limit": "100"},
            "request_headers": {
                "Authorization": "Bearer admin_token_123"
            }
        }
    ]
    
    print(f"准备了 {len(taobao_apis)} 个测试API")
    for i, api in enumerate(taobao_apis, 1):
        print(f"  {i}. {api['method']} {api['url']} - {api['description']}")
    print()
    
    # 2. 配置Agent
    print("2. 配置Agent")
    print("-" * 30)
    
    # 完整的配置
    comprehensive_config = {
        # 安全检测配置
        "security_checks": {
            "field_exposure": {
                "enabled": True,
                "max_test_iterations": 15,
                "test_different_users": True,
                "test_parameter_manipulation": True
            },
            "privilege_escalation": {
                "enabled": True,
                "test_horizontal_escalation": True,
                "test_vertical_escalation": True,
                "max_user_id_tests": 20
            },
            "anti_crawler": {
                "enabled": True,
                "rate_limit_test_count": 25,
                "user_agent_test_enabled": True,
                "captcha_detection_enabled": True,
                "behavior_analysis_enabled": True
            },
            "owasp_fuzzer": {
                "enabled": True,
                "max_iterations": 50,
                "timeout_per_test": 30,
                "concurrent_tests": 3
            }
        },
        
        # 智能分析配置
        "intelligent_analysis": {
            "enabled": True,
            "temperature": 0.1,
            "max_context_length": 8000,
            "analysis_types": {
                "risk_assessment": True,
                "vulnerability_correlation": True,
                "remediation_suggestions": True,
                "business_impact": True,
                "attack_scenarios": True,
                "comprehensive_summary": True
            }
        },
        
        # 报告生成配置
        "reporting": {
            "enabled": True,
            "format": "html",
            "include_charts": True,
            "include_raw_data": False,
            "report_title": "淘宝API安全评估报告",
            "theme": "default"
        },
        
        # 分析模式
        "analysis_mode": "comprehensive"
    }
    
    print("配置项:")
    print(f"  - 字段过度透出检测: {'启用' if comprehensive_config['security_checks']['field_exposure']['enabled'] else '禁用'}")
    print(f"  - 越权访问检测: {'启用' if comprehensive_config['security_checks']['privilege_escalation']['enabled'] else '禁用'}")
    print(f"  - 反爬策略检测: {'启用' if comprehensive_config['security_checks']['anti_crawler']['enabled'] else '禁用'}")
    print(f"  - OWASP Fuzz测试: {'启用' if comprehensive_config['security_checks']['owasp_fuzzer']['enabled'] else '禁用'}")
    print(f"  - LLM智能分析: {'启用' if comprehensive_config['intelligent_analysis']['enabled'] else '禁用'}")
    print(f"  - 智能报告生成: {'启用' if comprehensive_config['reporting']['enabled'] else '禁用'}")
    print()
    
    # 3. 初始化Agent
    print("3. 初始化Agent")
    print("-" * 30)
    
    try:
        agent = TaobaoAPISecurityAgent()
        print("✓ Agent初始化成功")
        print("✓ 安全检测模块加载完成")
        print("✓ LLM提供商配置完成")
        print("✓ 报告生成器准备就绪")
    except Exception as e:
        print(f"✗ Agent初始化失败: {e}")
        return
    print()
    
    # 4. 执行安全分析
    print("4. 执行安全分析")
    print("-" * 30)
    
    try:
        print("开始执行全面安全分析...")
        print("注意: 完整分析可能需要几分钟时间")
        print()
        
        # 执行分析
        result = agent.analyze(
            json.dumps(taobao_apis),
            InputType.API_LIST,
            comprehensive_config
        )
        
        print("✓ 安全分析完成")
        
    except Exception as e:
        print(f"✗ 安全分析失败: {e}")
        import traceback
        traceback.print_exc()
        return
    print()
    
    # 5. 分析结果
    print("5. 分析结果")
    print("-" * 30)
    
    final_report = result.get('final_report', {})
    summary = final_report.get('summary', {})
    
    print("基础统计:")
    print(f"  - 测试API数量: {summary.get('total_apis', 0)}")
    print(f"  - 发现漏洞总数: {summary.get('total_vulnerabilities', 0)}")
    print(f"  - 分析开始时间: {summary.get('start_time', 'N/A')}")
    print(f"  - 分析完成时间: {summary.get('analysis_time', 'N/A')}")
    print()
    
    # 安全问题统计
    security_results = final_report.get('security_results', [])
    if security_results:
        print("安全问题分布:")
        
        # 按风险等级统计
        risk_stats = {}
        vuln_type_stats = {}
        
        for result in security_results:
            risk_level = result.get('risk_level', 'unknown')
            vuln_type = result.get('check_type', 'unknown')
            
            risk_stats[risk_level] = risk_stats.get(risk_level, 0) + 1
            vuln_type_stats[vuln_type] = vuln_type_stats.get(vuln_type, 0) + 1
        
        print("  按风险等级:")
        for risk, count in sorted(risk_stats.items()):
            print(f"    {risk}: {count}个")
        
        print("  按漏洞类型:")
        for vuln_type, count in sorted(vuln_type_stats.items()):
            print(f"    {vuln_type}: {count}个")
        print()
        
        # 显示前5个高风险问题
        high_risk_issues = [
            result for result in security_results 
            if result.get('risk_level') in ['critical', 'high']
        ]
        
        if high_risk_issues:
            print("高风险问题 (前5个):")
            for i, issue in enumerate(high_risk_issues[:5], 1):
                print(f"  {i}. {issue.get('title', 'Unknown')}")
                print(f"     API: {issue.get('api_info', {}).get('url', 'Unknown')}")
                print(f"     风险: {issue.get('risk_level', 'Unknown')}")
                print(f"     类型: {issue.get('check_type', 'Unknown')}")
                print()
    
    # 6. 智能分析结果
    print("6. 智能分析结果")
    print("-" * 30)
    
    intelligent_analysis = final_report.get('intelligent_analysis', {})
    if intelligent_analysis:
        print("✓ LLM智能分析已完成")
        
        # 统计分析的API数量
        analyzed_apis = len([k for k in intelligent_analysis.keys() if k != 'comprehensive_summary'])
        print(f"  - 分析的API数量: {analyzed_apis}")
        
        # 检查综合总结
        if 'comprehensive_summary' in intelligent_analysis:
            print("  - 生成了综合安全总结")
        
        # 显示LLM提供商统计
        metadata = final_report.get('metadata', {})
        llm_stats = metadata.get('llm_provider_stats', {})
        if llm_stats:
            print("  - LLM使用统计:")
            for provider, stats in llm_stats.items():
                print(f"    {provider}: {stats.get('request_count', 0)}次请求")
    else:
        print("⚠ 未生成智能分析结果")
        print("  可能原因: LLM API密钥未配置或网络问题")
    print()
    
    # 7. 报告生成结果
    print("7. 报告生成结果")
    print("-" * 30)
    
    enhanced_report = final_report.get('enhanced_report')
    if enhanced_report and enhanced_report.get('success'):
        report_info = enhanced_report.get('report_result', {})
        print("✓ 智能报告生成成功")
        print(f"  - 格式: {report_info.get('format', 'Unknown')}")
        print(f"  - 文件名: {report_info.get('filename', 'Unknown')}")
        print(f"  - 文件大小: {report_info.get('size', 0)} 字节")
        print(f"  - 文件路径: {report_info.get('file_path', 'Unknown')}")
        
        # 如果是HTML报告，提供打开建议
        if report_info.get('format') == 'html':
            print(f"  - 可以在浏览器中打开查看详细报告")
    else:
        print("⚠ 报告生成失败或未启用")
        if enhanced_report and not enhanced_report.get('success'):
            print(f"  错误: {enhanced_report.get('error', 'Unknown')}")
    print()
    
    # 8. 总结和建议
    print("8. 总结和建议")
    print("-" * 30)
    
    total_issues = summary.get('total_vulnerabilities', 0)
    if total_issues > 0:
        print(f"⚠ 发现 {total_issues} 个安全问题，建议:")
        print("  1. 优先修复高风险和严重风险问题")
        print("  2. 查看详细的智能分析报告")
        print("  3. 按照修复建议进行安全加固")
        print("  4. 定期进行安全检测")
    else:
        print("✓ 未发现明显的安全问题")
        print("  建议继续保持良好的安全实践")
    
    print()
    print("分析完成！感谢使用淘宝API安全检测Agent")


async def main():
    """主函数"""
    try:
        await complete_workflow_example()
    except KeyboardInterrupt:
        print("\n用户中断了分析过程")
    except Exception as e:
        print(f"\n示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
