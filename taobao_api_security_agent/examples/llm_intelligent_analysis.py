"""
LLM智能分析示例

展示如何使用LLM进行智能安全分析和报告生成。
"""

import json
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.main_agent import TaobaoAPISecurityAgent
from src.agent.state import InputType
from src.integrations.llm_provider import LLMProvider
from src.security.intelligent_analyzer import IntelligentSecurityAnalyzer


async def example_llm_provider_test():
    """示例：测试LLM提供商"""
    print("=== LLM提供商测试 ===")
    
    # 模拟LLM配置
    llm_config = {
        "default_provider": "claude",
        "fallback_strategy": {
            "enabled": True,
            "priority_order": ["claude", "gemini", "qwen"]
        },
        "providers": {
            "claude": {
                "enabled": True,
                "model": "claude-3-sonnet-20240229",
                "api_key": "your_claude_api_key_here",  # 需要真实的API密钥
                "max_tokens": 4096,
                "temperature": 0.1
            },
            "gemini": {
                "enabled": False,  # 暂时禁用
                "model": "gemini-pro",
                "api_key": "your_gemini_api_key_here",
                "max_tokens": 4096,
                "temperature": 0.1
            },
            "qwen": {
                "enabled": False,  # 暂时禁用
                "model": "qwen-turbo",
                "api_key": "your_qwen_api_key_here",
                "base_url": "https://dashscope.aliyuncs.com/api/v1",
                "max_tokens": 4096,
                "temperature": 0.1
            }
        }
    }
    
    try:
        # 初始化LLM提供商
        llm_provider = LLMProvider(llm_config)
        
        print(f"可用的LLM提供商: {llm_provider.get_available_providers()}")
        
        if not llm_provider.get_available_providers():
            print("警告: 没有可用的LLM提供商，请配置API密钥")
            return
        
        # 测试简单的对话
        test_messages = [
            {"role": "user", "content": "请简单介绍一下API安全测试的重要性"}
        ]
        
        response = await llm_provider.generate_response(
            messages=test_messages,
            system_prompt="你是一个API安全专家，请提供专业的建议。"
        )
        
        print(f"LLM响应:")
        print(f"提供商: {response.provider}")
        print(f"模型: {response.model}")
        print(f"响应时间: {response.response_time:.2f}秒")
        print(f"内容: {response.content[:200]}...")
        
        # 显示使用统计
        stats = llm_provider.get_provider_stats()
        print(f"\n使用统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"LLM提供商测试失败: {e}")


async def example_intelligent_security_analysis():
    """示例：智能安全分析"""
    print("=== 智能安全分析示例 ===")
    
    # 模拟安全检测结果
    from src.agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel
    from datetime import datetime
    from urllib.parse import urlparse
    
    # 创建模拟API信息
    api_url = "https://main.m.taobao.com/api/user/profile"
    parsed_url = urlparse(api_url)
    api_info = APIInfo(
        url=api_url,
        method="GET",
        domain=parsed_url.netloc,
        path=parsed_url.path,
        request_params={"userId": "12345"},
        timestamp=datetime.now(),
        source='test'
    )
    
    # 创建模拟安全检测结果
    security_results = [
        SecurityCheckResult(
            check_type=VulnerabilityType.FIELD_EXPOSURE,
            risk_level=RiskLevel.HIGH,
            is_vulnerable=True,
            title="敏感信息泄露",
            description="API响应包含用户手机号和身份证号",
            technical_details={
                "exposed_fields": ["phone", "idCard"],
                "response_size": 1024
            },
            evidence=["检测到手机号: 138****1234", "检测到身份证: 110101********1234"],
            recommendation="移除或脱敏响应中的敏感信息",
            api_info=api_info
        ),
        SecurityCheckResult(
            check_type=VulnerabilityType.OWASP_API1_BOLA,
            risk_level=RiskLevel.MEDIUM,
            is_vulnerable=True,
            title="水平越权访问",
            description="通过修改userId参数可以访问其他用户数据",
            technical_details={
                "parameter": "userId",
                "test_values": ["0", "1", "999999"]
            },
            evidence=["参数userId=999999返回了其他用户数据"],
            recommendation="实施严格的对象级访问控制",
            api_info=api_info
        )
    ]
    
    try:
        # 初始化智能分析器（使用模拟配置）
        llm_config = {
            "default_provider": "mock",  # 使用模拟提供商
            "providers": {
                "mock": {
                    "enabled": True,
                    "model": "mock-model"
                }
            }
        }
        
        # 这里在实际环境中需要真实的LLM配置
        print("注意: 此示例需要配置真实的LLM API密钥才能运行")
        print("模拟智能分析过程...")
        
        # 模拟分析结果
        mock_analysis_results = {
            "risk_assessment": {
                "risk_analysis": {
                    "overall_risk_score": 7.5,
                    "critical_issues": 0,
                    "high_risk_issues": 1,
                    "medium_risk_issues": 1,
                    "risk_factors": [
                        "敏感信息泄露风险较高",
                        "存在水平越权访问漏洞",
                        "缺少适当的访问控制机制"
                    ]
                }
            },
            "vulnerability_correlation": {
                "correlation_analysis": {
                    "related_vulnerabilities": [
                        {
                            "primary": "敏感信息泄露",
                            "secondary": "水平越权访问",
                            "relationship": "两个漏洞结合可能导致大规模用户数据泄露"
                        }
                    ],
                    "attack_chains": [
                        "攻击者通过越权访问获取大量用户敏感信息"
                    ]
                }
            },
            "remediation_suggestions": {
                "remediation_suggestions": {
                    "immediate_actions": [
                        "立即对API响应进行敏感信息脱敏",
                        "实施用户ID验证机制",
                        "添加访问日志监控"
                    ],
                    "long_term_improvements": [
                        "建立完整的API安全治理体系",
                        "实施自动化安全测试",
                        "定期进行安全审计"
                    ]
                }
            },
            "business_impact": {
                "business_impact": {
                    "data_breach_risk": "高",
                    "compliance_impact": "可能违反个人信息保护法规",
                    "reputation_damage": "中等",
                    "financial_impact": "可能面临监管罚款"
                }
            },
            "attack_scenarios": {
                "attack_scenarios": {
                    "scenario_1": {
                        "name": "批量用户数据窃取",
                        "steps": [
                            "攻击者发现userId参数可以枚举",
                            "编写脚本批量请求不同用户ID",
                            "收集大量用户敏感信息",
                            "在暗网出售或用于其他恶意目的"
                        ],
                        "impact": "大规模用户隐私泄露"
                    }
                }
            }
        }
        
        print("智能分析结果:")
        print(f"整体风险评分: {mock_analysis_results['risk_assessment']['risk_analysis']['overall_risk_score']}/10")
        print(f"高风险问题: {mock_analysis_results['risk_assessment']['risk_analysis']['high_risk_issues']}个")
        print(f"中风险问题: {mock_analysis_results['risk_assessment']['risk_analysis']['medium_risk_issues']}个")
        
        print("\n关键风险因素:")
        for factor in mock_analysis_results['risk_assessment']['risk_analysis']['risk_factors']:
            print(f"- {factor}")
        
        print("\n立即行动建议:")
        for action in mock_analysis_results['remediation_suggestions']['remediation_suggestions']['immediate_actions']:
            print(f"- {action}")
        
        print("\n攻击场景分析:")
        scenario = mock_analysis_results['attack_scenarios']['attack_scenarios']['scenario_1']
        print(f"场景: {scenario['name']}")
        print(f"影响: {scenario['impact']}")
        
    except Exception as e:
        print(f"智能安全分析失败: {e}")


async def example_comprehensive_analysis_with_llm():
    """示例：使用LLM的综合分析"""
    print("=== 使用LLM的综合分析示例 ===")
    
    # 定义包含智能分析的配置
    analysis_config = {
        "security_checks": {
            "field_exposure": {"enabled": True},
            "privilege_escalation": {"enabled": True},
            "anti_crawler": {"enabled": True}
        },
        "intelligent_analysis": {
            "enabled": True,
            "temperature": 0.1,
            "max_context_length": 8000
        },
        "reporting": {
            "enabled": True,
            "format": "html",
            "include_charts": True,
            "include_raw_data": False
        }
    }
    
    # 测试API列表
    test_apis = [
        {
            "url": "https://main.m.taobao.com/api/user/profile",
            "method": "GET",
            "description": "用户资料接口"
        },
        {
            "url": "https://www.taobao.com/api/product/search",
            "method": "GET", 
            "description": "商品搜索接口"
        }
    ]
    
    try:
        # 初始化Agent
        agent = TaobaoAPISecurityAgent()
        
        print("开始综合安全分析（包含LLM智能分析）...")
        print("注意: 需要配置真实的LLM API密钥才能获得智能分析结果")
        
        # 执行分析
        result = agent.analyze(
            json.dumps(test_apis),
            InputType.API_LIST,
            analysis_config
        )
        
        # 输出结果
        print(f"\n分析完成!")
        print(f"总API数量: {result['final_report']['summary']['total_apis']}")
        print(f"发现漏洞数量: {result['final_report']['summary']['total_vulnerabilities']}")
        
        # 检查是否有智能分析结果
        intelligent_analysis = result['final_report'].get('intelligent_analysis', {})
        if intelligent_analysis:
            print(f"\n智能分析结果:")
            print(f"分析的API数量: {len([k for k in intelligent_analysis.keys() if k != 'comprehensive_summary'])}")
            
            if 'comprehensive_summary' in intelligent_analysis:
                summary = intelligent_analysis['comprehensive_summary']
                print(f"生成了综合安全总结")
        else:
            print("\n未生成智能分析结果（可能是LLM配置问题）")
        
        # 检查增强报告
        enhanced_report = result['final_report'].get('enhanced_report')
        if enhanced_report and enhanced_report.get('success'):
            report_info = enhanced_report['report_result']
            print(f"\n生成了增强报告:")
            print(f"格式: {report_info['format']}")
            print(f"文件: {report_info['filename']}")
            print(f"大小: {report_info['size']} 字节")
        else:
            print("\n未生成增强报告")
            if enhanced_report and not enhanced_report.get('success'):
                print(f"报告生成错误: {enhanced_report.get('error')}")
        
    except Exception as e:
        print(f"综合分析失败: {e}")
        import traceback
        traceback.print_exc()


async def example_report_generation():
    """示例：智能报告生成"""
    print("=== 智能报告生成示例 ===")
    
    print("此示例展示如何生成包含LLM智能分析的专业安全报告")
    print("报告特性:")
    print("- 执行摘要（LLM生成）")
    print("- 关键发现分析")
    print("- 智能修复建议")
    print("- 业务影响评估")
    print("- 攻击场景分析")
    print("- 可视化图表")
    print("- 多格式输出（HTML、JSON、Markdown）")
    
    print("\n要使用此功能，请:")
    print("1. 配置LLM API密钥（Claude、Gemini或Qwen）")
    print("2. 运行完整的安全分析")
    print("3. 查看生成的reports目录")


async def main():
    """主函数"""
    print("淘宝API安全检测Agent - LLM智能分析示例")
    print("=" * 60)
    
    try:
        # 运行各种LLM智能分析示例
        await example_llm_provider_test()
        print()
        
        await example_intelligent_security_analysis()
        print()
        
        await example_comprehensive_analysis_with_llm()
        print()
        
        await example_report_generation()
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
