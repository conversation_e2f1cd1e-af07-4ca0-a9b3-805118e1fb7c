"""
智能Fuzz测试示例

展示如何使用智能Fuzz策略进行API安全测试。
"""

import json
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.security.intelligent_fuzzer import IntelligentFuzzer, ParameterType, RiskDimension
from src.agent.state import APIInfo
from datetime import datetime
from urllib.parse import urlparse


async def example_parameter_analysis():
    """示例：参数分析和画像生成"""
    print("=== 参数分析和画像生成示例 ===")
    
    # 初始化智能Fuzz测试器
    fuzzer = IntelligentFuzzer({
        'max_fuzz_per_param': 15,
        'enable_deep_fuzz': True,
        'risk_threshold': 6.0
    })
    
    # 模拟淘宝用户资料API
    api_url = "https://main.m.taobao.com/api/user/profile"
    parsed_url = urlparse(api_url)
    api_info = APIInfo(
        url=api_url,
        method="GET",
        domain=parsed_url.netloc,
        path=parsed_url.path,
        timestamp=datetime.now(),
        source='test'
    )
    
    # 模拟请求参数
    request_params = {
        'userId': '12345',
        'fields': 'basic,contact',
        'format': 'json',
        'token': 'abc123def456',
        'callback': 'https://example.com/callback',
        'page': '1',
        'size': '20',
        'admin': 'false',
        'debug': '0'
    }
    
    print(f"分析API: {api_url}")
    print(f"参数数量: {len(request_params)}")
    print()
    
    # 分析参数
    profiles = fuzzer.analyze_parameters(api_info, request_params)
    
    print("参数分析结果:")
    print("-" * 80)
    
    for profile in profiles:
        print(f"参数名: {profile.name}")
        print(f"  类型: {profile.param_type.value}")
        print(f"  敏感度评分: {profile.sensitivity_score:.1f}/10")
        print(f"  Fuzz优先级: {profile.fuzz_priority}/5")
        print(f"  风险维度: {[dim.value for dim in profile.risk_dimensions]}")
        if profile.context_hints:
            print(f"  上下文提示: {', '.join(profile.context_hints)}")
        print()
    
    return profiles, api_info, fuzzer


async def example_payload_generation():
    """示例：智能载荷生成"""
    print("=== 智能载荷生成示例 ===")
    
    profiles, api_info, fuzzer = await example_parameter_analysis()
    
    # 选择高优先级参数进行载荷生成
    high_priority_profiles = [p for p in profiles if p.fuzz_priority >= 4]
    
    print(f"为 {len(high_priority_profiles)} 个高优先级参数生成载荷:")
    print("-" * 80)
    
    for profile in high_priority_profiles:
        print(f"\n参数: {profile.name} (优先级: {profile.fuzz_priority})")
        
        # 生成载荷
        payloads = fuzzer.generate_intelligent_payloads(profile, api_info)
        
        print(f"生成载荷数量: {len(payloads)}")
        
        # 按载荷类型分组显示
        payload_groups = {}
        for payload_type, payload_value, description in payloads:
            if payload_type not in payload_groups:
                payload_groups[payload_type] = []
            payload_groups[payload_type].append((payload_value, description))
        
        for payload_type, items in payload_groups.items():
            print(f"  {payload_type}:")
            for value, desc in items[:3]:  # 只显示前3个
                print(f"    - {value} ({desc})")
            if len(items) > 3:
                print(f"    ... 还有 {len(items) - 3} 个载荷")
        print()


async def example_risk_dimension_analysis():
    """示例：风险维度分析"""
    print("=== 风险维度分析示例 ===")
    
    # 不同类型API的风险维度分析
    test_apis = [
        {
            'name': '用户资料接口',
            'url': 'https://main.m.taobao.com/api/user/profile',
            'params': {'userId': '12345', 'fields': 'basic'}
        },
        {
            'name': '商品搜索接口',
            'url': 'https://www.taobao.com/api/product/search',
            'params': {'q': 'iPhone', 'category': 'electronics', 'sort': 'price'}
        },
        {
            'name': '管理员用户接口',
            'url': 'https://www.taobao.com/api/admin/users',
            'params': {'role': 'user', 'status': 'active', 'limit': '10'}
        },
        {
            'name': '订单创建接口',
            'url': 'https://trade.taobao.com/api/order/create',
            'params': {'itemId': '67890', 'quantity': '1', 'price': '99.99'}
        }
    ]
    
    fuzzer = IntelligentFuzzer()
    
    for api_data in test_apis:
        print(f"\n{api_data['name']}: {api_data['url']}")
        print("-" * 60)
        
        # 创建API信息
        parsed_url = urlparse(api_data['url'])
        api_info = APIInfo(
            url=api_data['url'],
            method="GET",
            domain=parsed_url.netloc,
            path=parsed_url.path,
            timestamp=datetime.now(),
            source='test'
        )
        
        # 分析参数
        profiles = fuzzer.analyze_parameters(api_info, api_data['params'])
        
        # 统计风险维度
        all_dimensions = set()
        for profile in profiles:
            all_dimensions.update(profile.risk_dimensions)
        
        print(f"涉及的风险维度: {len(all_dimensions)}个")
        for dimension in sorted(all_dimensions, key=lambda x: x.value):
            # 统计该维度下的参数
            params_in_dimension = [
                p.name for p in profiles 
                if dimension in p.risk_dimensions
            ]
            print(f"  {dimension.value}: {', '.join(params_in_dimension)}")
        
        # 显示最高风险的参数
        highest_risk_param = max(profiles, key=lambda x: x.sensitivity_score)
        print(f"最高风险参数: {highest_risk_param.name} (评分: {highest_risk_param.sensitivity_score:.1f})")


async def example_business_logic_fuzzing():
    """示例：业务逻辑Fuzz测试"""
    print("=== 业务逻辑Fuzz测试示例 ===")
    
    # 电商业务场景的特殊测试
    ecommerce_scenarios = [
        {
            'scenario': '价格操作测试',
            'api': 'https://cart.taobao.com/api/cart/add',
            'params': {'itemId': '12345', 'price': '99.99', 'quantity': '1'},
            'focus_param': 'price',
            'business_risks': [
                '负价格攻击', '零价格攻击', '极小价格攻击', '价格溢出攻击'
            ]
        },
        {
            'scenario': '数量操作测试',
            'api': 'https://cart.taobao.com/api/cart/update',
            'params': {'cartId': 'cart123', 'quantity': '2'},
            'focus_param': 'quantity',
            'business_risks': [
                '负数量攻击', '零数量攻击', '超大数量攻击', '小数数量攻击'
            ]
        },
        {
            'scenario': '折扣操作测试',
            'api': 'https://promotion.taobao.com/api/coupon/apply',
            'params': {'couponId': 'coupon123', 'discount': '10'},
            'focus_param': 'discount',
            'business_risks': [
                '负折扣攻击', '超100%折扣攻击', '折扣叠加攻击'
            ]
        }
    ]
    
    fuzzer = IntelligentFuzzer({'enable_deep_fuzz': True})
    
    for scenario in ecommerce_scenarios:
        print(f"\n{scenario['scenario']}")
        print(f"API: {scenario['api']}")
        print(f"焦点参数: {scenario['focus_param']}")
        print("-" * 60)
        
        # 创建API信息
        parsed_url = urlparse(scenario['api'])
        api_info = APIInfo(
            url=scenario['api'],
            method="POST",
            domain=parsed_url.netloc,
            path=parsed_url.path,
            timestamp=datetime.now(),
            source='test'
        )
        
        # 分析参数
        profiles = fuzzer.analyze_parameters(api_info, scenario['params'])
        
        # 找到焦点参数
        focus_profile = next(
            (p for p in profiles if p.name == scenario['focus_param']), 
            None
        )
        
        if focus_profile:
            print(f"参数类型: {focus_profile.param_type.value}")
            print(f"敏感度评分: {focus_profile.sensitivity_score:.1f}")
            print(f"风险维度: {[dim.value for dim in focus_profile.risk_dimensions]}")
            
            # 生成业务逻辑载荷
            payloads = fuzzer.generate_intelligent_payloads(focus_profile, api_info)
            business_payloads = [
                (ptype, pvalue, pdesc) for ptype, pvalue, pdesc in payloads
                if ptype == 'business_logic'
            ]
            
            print(f"\n业务逻辑测试载荷 ({len(business_payloads)}个):")
            for payload_type, payload_value, description in business_payloads:
                print(f"  - {payload_value} ({description})")
            
            print(f"\n潜在业务风险:")
            for risk in scenario['business_risks']:
                print(f"  - {risk}")


async def example_context_aware_fuzzing():
    """示例：上下文感知Fuzz测试"""
    print("=== 上下文感知Fuzz测试示例 ===")
    
    # 不同上下文的API需要不同的测试策略
    context_examples = [
        {
            'context': '用户认证上下文',
            'api': 'https://login.taobao.com/api/auth/login',
            'params': {'username': 'testuser', 'password': 'password123'},
            'context_hints': [
                '认证绕过测试', 'SQL注入测试', '暴力破解防护测试'
            ]
        },
        {
            'context': '管理后台上下文',
            'api': 'https://admin.taobao.com/api/system/config',
            'params': {'configKey': 'site_name', 'configValue': 'Taobao'},
            'context_hints': [
                '权限提升测试', '配置注入测试', '敏感信息泄露测试'
            ]
        },
        {
            'context': '文件上传上下文',
            'api': 'https://upload.taobao.com/api/file/upload',
            'params': {'file': 'image.jpg', 'type': 'avatar'},
            'context_hints': [
                '文件类型绕过测试', '路径遍历测试', '恶意文件上传测试'
            ]
        }
    ]
    
    fuzzer = IntelligentFuzzer()
    
    for example in context_examples:
        print(f"\n{example['context']}")
        print(f"API: {example['api']}")
        print("-" * 60)
        
        # 创建API信息
        parsed_url = urlparse(example['api'])
        api_info = APIInfo(
            url=example['api'],
            method="POST",
            domain=parsed_url.netloc,
            path=parsed_url.path,
            timestamp=datetime.now(),
            source='test'
        )
        
        # 分析参数
        profiles = fuzzer.analyze_parameters(api_info, example['params'])
        
        print("参数分析结果:")
        for profile in profiles:
            print(f"  {profile.name}: {profile.param_type.value} "
                  f"(敏感度: {profile.sensitivity_score:.1f}, "
                  f"优先级: {profile.fuzz_priority})")
            
            if profile.context_hints:
                print(f"    上下文提示: {', '.join(profile.context_hints)}")
        
        print(f"\n建议的测试策略:")
        for hint in example['context_hints']:
            print(f"  - {hint}")


async def main():
    """主函数"""
    print("淘宝API安全检测Agent - 智能Fuzz测试示例")
    print("=" * 80)
    
    try:
        await example_parameter_analysis()
        print("\n" + "=" * 80 + "\n")
        
        await example_payload_generation()
        print("\n" + "=" * 80 + "\n")
        
        await example_risk_dimension_analysis()
        print("\n" + "=" * 80 + "\n")
        
        await example_business_logic_fuzzing()
        print("\n" + "=" * 80 + "\n")
        
        await example_context_aware_fuzzing()
        
        print("\n" + "=" * 80)
        print("智能Fuzz测试示例完成！")
        print("\n关键特性:")
        print("✓ 参数类型智能识别")
        print("✓ 敏感度评分算法")
        print("✓ 风险维度多维分析")
        print("✓ 上下文感知载荷生成")
        print("✓ 业务逻辑专项测试")
        print("✓ 优先级智能排序")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
