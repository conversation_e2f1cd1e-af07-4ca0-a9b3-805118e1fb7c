"""
使用示例

展示如何使用淘宝API安全检测Agent。
"""

import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.main_agent import TaobaoAPISecurityAgent
from src.agent.state import InputType


def example_analyze_api_list():
    """示例：分析API列表"""
    print("=== 示例：分析API列表 ===")
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    # 定义要检测的API列表
    api_urls = [
        "https://main.m.taobao.com/api/user/info",
        "https://www.taobao.com/api/product/search",
        "https://cart.taobao.com/api/cart/list",
        "https://trade.taobao.com/api/order/create"
    ]
    
    try:
        # 执行分析
        result = agent.analyze_api_list(api_urls)
        
        # 输出结果
        print(f"分析完成！")
        print(f"总API数量: {result['final_report']['summary']['total_apis']}")
        print(f"发现漏洞数量: {result['final_report']['summary']['total_vulnerabilities']}")
        
        # 显示发现的安全问题
        if result['security_results']:
            print("\n发现的安全问题:")
            for i, security_result in enumerate(result['security_results'], 1):
                print(f"{i}. {security_result.title}")
                print(f"   风险等级: {security_result.risk_level}")
                print(f"   API: {security_result.api_info.url}")
                print(f"   描述: {security_result.description}")
                print()
        else:
            print("未发现明显的安全问题")
            
    except Exception as e:
        print(f"分析失败: {e}")


def example_analyze_json_api_list():
    """示例：分析JSON格式的API列表"""
    print("=== 示例：分析JSON格式的API列表 ===")
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    # JSON格式的API列表
    api_list_json = json.dumps([
        {
            "url": "https://main.m.taobao.com/api/user/profile",
            "method": "GET",
            "description": "获取用户资料"
        },
        {
            "url": "https://www.taobao.com/api/admin/users",
            "method": "POST",
            "description": "管理员用户接口"
        },
        {
            "url": "https://passport.taobao.com/api/login",
            "method": "POST",
            "description": "用户登录接口"
        }
    ])
    
    try:
        # 执行分析
        result = agent.analyze(api_list_json, InputType.API_LIST)
        
        # 输出结果摘要
        summary = result['final_report']['summary']
        print(f"分析完成！")
        print(f"总API数量: {summary['total_apis']}")
        print(f"发现漏洞数量: {summary['total_vulnerabilities']}")
        print(f"分析时间: {summary['analysis_time']}")
        
    except Exception as e:
        print(f"分析失败: {e}")


def example_analyze_har_data():
    """示例：分析HAR数据"""
    print("=== 示例：分析HAR数据 ===")
    
    # 模拟HAR数据
    har_data = {
        "log": {
            "version": "1.2",
            "entries": [
                {
                    "request": {
                        "method": "GET",
                        "url": "https://main.m.taobao.com/api/user/info",
                        "headers": [
                            {"name": "User-Agent", "value": "Mozilla/5.0"},
                            {"name": "Cookie", "value": "_tb_token_=abc123"}
                        ]
                    },
                    "response": {
                        "status": 200,
                        "headers": [
                            {"name": "Content-Type", "value": "application/json"}
                        ],
                        "content": {
                            "text": '{"userId": "12345", "nick": "testuser", "phone": "138****1234"}'
                        }
                    },
                    "startedDateTime": "2024-01-01T10:00:00.000Z"
                }
            ]
        }
    }
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    try:
        # 执行分析
        result = agent.analyze_har_file(json.dumps(har_data))
        
        # 输出结果
        print(f"HAR数据分析完成！")
        print(f"解析出的API数量: {len(result['apis'])}")
        
        if result['apis']:
            print("\n解析出的API:")
            for api in result['apis']:
                print(f"- {api.method} {api.url}")
        
    except Exception as e:
        print(f"HAR数据分析失败: {e}")


def example_analyze_web_page():
    """示例：分析网页内容"""
    print("=== 示例：分析网页内容 ===")
    
    # 模拟HTML内容
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>淘宝页面</title>
    </head>
    <body>
        <div id="app">
            <script>
                // API调用示例
                fetch('/api/user/info').then(response => response.json());
                
                $.ajax({
                    url: '/mtop.taobao.detail.getdetail',
                    method: 'GET'
                });
                
                window.apiConfig = {
                    "userApi": "/api/user/profile",
                    "productApi": "/api/product/list"
                };
            </script>
        </div>
        
        <form action="/api/user/update" method="post">
            <input type="text" name="username">
        </form>
        
        <a href="/api/admin/dashboard">管理后台</a>
    </body>
    </html>
    """
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    try:
        # 执行分析
        result = agent.analyze_web_page(html_content, "https://www.taobao.com")
        
        # 输出结果
        print(f"网页内容分析完成！")
        print(f"提取出的API数量: {len(result['apis'])}")
        
        if result['apis']:
            print("\n提取出的API:")
            for api in result['apis']:
                print(f"- {api.method} {api.url}")
        
    except Exception as e:
        print(f"网页内容分析失败: {e}")


def example_custom_analysis():
    """示例：自定义分析配置"""
    print("=== 示例：自定义分析配置 ===")
    
    # 初始化Agent
    agent = TaobaoAPISecurityAgent()
    
    # 自定义分析配置
    custom_config = {
        "security_checks": {
            "field_exposure": {"enabled": True},
            "privilege_escalation": {"enabled": True},
            "anti_crawler": {"enabled": False},  # 禁用反爬检测
            "owasp_fuzzer": {"enabled": False}   # 禁用OWASP检测
        },
        "analysis_mode": "fast"  # 快速模式
    }
    
    # API列表
    api_list = [
        {"url": "https://www.taobao.com/api/user/sensitive", "method": "GET"}
    ]
    
    try:
        # 执行自定义分析
        result = agent.analyze(
            json.dumps(api_list),
            InputType.API_LIST,
            custom_config
        )
        
        print(f"自定义分析完成！")
        print(f"使用配置: {custom_config['analysis_mode']} 模式")
        
    except Exception as e:
        print(f"自定义分析失败: {e}")


def main():
    """主函数"""
    print("淘宝API安全检测Agent - 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_analyze_api_list()
        print()
        
        example_analyze_json_api_list()
        print()
        
        example_analyze_har_data()
        print()
        
        example_analyze_web_page()
        print()
        
        example_custom_analysis()
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
