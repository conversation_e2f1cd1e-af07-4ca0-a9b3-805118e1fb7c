"""
企业级架构演示

展示基于架构图的企业级API安全检测系统功能。
"""

import json
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.enterprise.asset_discovery import APIAssetDiscoveryEngine


async def demo_api_asset_discovery():
    """演示：API资产发现引擎"""
    print("=== API资产发现引擎演示 ===")
    print()
    
    # 1. 初始化资产发现引擎
    print("1. 初始化API资产发现引擎")
    print("-" * 50)
    
    discovery_config = {
        'discovery_methods': [
            'traffic_analysis',
            'web_crawling',
            'subdomain_enumeration', 
            'documentation_parsing',
            'port_scanning'
        ],
        'max_crawl_depth': 2,
        'max_pages_per_domain': 100,
        'concurrent_requests': 5,
        'exclusion_patterns': [
            r'.*\.(css|js|png|jpg|jpeg|gif|ico|svg)$',
            r'.*/static/.*',
            r'.*/assets/.*'
        ]
    }
    
    discovery_engine = APIAssetDiscoveryEngine(discovery_config)
    
    print("✓ 资产发现引擎初始化完成")
    print(f"✓ 启用发现方法: {', '.join(discovery_config['discovery_methods'])}")
    print(f"✓ 最大爬虫深度: {discovery_config['max_crawl_depth']}")
    print(f"✓ 并发请求数: {discovery_config['concurrent_requests']}")
    print()
    
    # 2. 执行资产发现
    print("2. 执行API资产发现")
    print("-" * 50)
    
    target_domains = [
        'main.m.taobao.com',
        'www.taobao.com',
        'cart.taobao.com',
        'trade.taobao.com',
        'admin.taobao.com'
    ]
    
    print(f"目标域名: {', '.join(target_domains)}")
    print("开始资产发现...")
    print()
    
    try:
        # 执行发现
        discovered_assets = await discovery_engine.discover_assets(target_domains)
        
        print(f"✓ 资产发现完成，共发现 {len(discovered_assets)} 个API")
        print()
        
        # 3. 分析发现结果
        print("3. 发现结果分析")
        print("-" * 50)
        
        # 按发现方法统计
        stats = discovery_engine.get_discovery_statistics()
        print("按发现方法统计:")
        for method, count in stats['by_method'].items():
            print(f"  {method}: {count} 个API")
        print()
        
        # 按域名统计
        print("按域名统计:")
        for domain, count in stats['by_domain'].items():
            print(f"  {domain}: {count} 个API")
        print()
        
        # 按业务关键性统计
        criticality_stats = {}
        for asset in discovered_assets:
            level = asset.business_criticality
            criticality_stats[level] = criticality_stats.get(level, 0) + 1
        
        print("按业务关键性统计:")
        for level in ['critical', 'high', 'medium', 'low']:
            count = criticality_stats.get(level, 0)
            if count > 0:
                print(f"  {level}: {count} 个API")
        print()
        
        # 4. 展示发现的API详情
        print("4. 发现的API详情 (前10个)")
        print("-" * 50)
        
        for i, asset in enumerate(discovered_assets[:10], 1):
            print(f"{i}. {asset.method} {asset.url}")
            print(f"   域名: {asset.domain}")
            print(f"   路径: {asset.path}")
            print(f"   发现方法: {asset.discovery_method}")
            print(f"   业务关键性: {asset.business_criticality}")
            print(f"   需要认证: {'是' if asset.authentication_required else '否'}")
            if asset.tags:
                print(f"   标签: {', '.join(asset.tags)}")
            if asset.parameters:
                print(f"   参数: {list(asset.parameters.keys())}")
            print()
        
        # 5. 导出资产数据
        print("5. 导出资产数据")
        print("-" * 50)
        
        # 导出为JSON格式
        exported_data = discovery_engine.export_assets('json')
        
        # 保存到文件
        output_file = project_root / 'reports' / 'discovered_api_assets.json'
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(exported_data)
        
        print(f"✓ 资产数据已导出到: {output_file}")
        print(f"✓ 导出格式: JSON")
        print(f"✓ 文件大小: {len(exported_data)} 字节")
        
    except Exception as e:
        print(f"✗ 资产发现失败: {e}")
        import traceback
        traceback.print_exc()


async def demo_enterprise_architecture_overview():
    """演示：企业级架构概览"""
    print("=== 企业级API安全检测系统架构概览 ===")
    print()
    
    architecture_layers = {
        "用户与展现层": {
            "description": "提供用户界面和管理功能",
            "components": [
                "安全运营平台 / Dashboard",
                "任务管理与策略配置",
                "报表与告警中心"
            ],
            "features": [
                "统一管控界面",
                "可视化监控",
                "策略配置管理",
                "多租户支持"
            ]
        },
        
        "核心处理层": {
            "description": "执行核心安全检测和分析功能",
            "components": [
                "API资产发现引擎",
                "安全检测引擎", 
                "风险分析引擎"
            ],
            "features": [
                "多方式资产发现",
                "全面安全检测",
                "智能风险分析",
                "LLM增强分析"
            ]
        },
        
        "数据存储层": {
            "description": "存储和管理各类数据",
            "components": [
                "API资产库",
                "漏洞与风险库",
                "配置数据库",
                "日志存储"
            ],
            "features": [
                "高可用存储",
                "数据备份",
                "快速检索",
                "数据安全"
            ]
        },
        
        "数据采集与执行层": {
            "description": "采集数据和执行检测任务",
            "components": [
                "流量采集代理",
                "智能Mobile Agent",
                "Web自动化爬虫",
                "检测流量发送器"
            ],
            "features": [
                "多源数据采集",
                "移动端支持",
                "自动化测试",
                "流量重放"
            ]
        },
        
        "基础设施与对抗层": {
            "description": "提供基础设施和反检测能力",
            "components": [
                "手机群控平台",
                "云主机/VM集群",
                "设备层风控对抗",
                "网络代理池"
            ],
            "features": [
                "设备指纹伪装",
                "行为模拟",
                "IP轮换",
                "反检测技术"
            ]
        }
    }
    
    print("系统架构分层:")
    print("=" * 80)
    
    for layer_name, layer_info in architecture_layers.items():
        print(f"\n📋 {layer_name}")
        print(f"   描述: {layer_info['description']}")
        print(f"   组件:")
        for component in layer_info['components']:
            print(f"     • {component}")
        print(f"   特性:")
        for feature in layer_info['features']:
            print(f"     ✓ {feature}")
    
    print("\n" + "=" * 80)
    print("🎯 核心技术优势:")
    print("  • 企业级高可用架构")
    print("  • 多维度安全检测能力") 
    print("  • 智能化风险分析")
    print("  • 强大的反检测对抗能力")
    print("  • 可扩展的微服务架构")
    print("  • 完整的数据治理体系")


async def demo_system_capabilities():
    """演示：系统能力展示"""
    print("=== 系统核心能力展示 ===")
    print()
    
    capabilities = {
        "🔍 资产发现能力": {
            "methods": [
                "流量分析发现 - 被动监听网络流量，自动识别API调用",
                "Web爬虫发现 - 主动爬取网站，发现API端点",
                "子域名枚举 - 发现隐藏的API子域名",
                "文档解析发现 - 解析Swagger、OpenAPI等文档",
                "端口扫描发现 - 扫描常见API服务端口"
            ],
            "coverage": "全面覆盖已知和未知API资产"
        },
        
        "🛡️ 安全检测能力": {
            "methods": [
                "OWASP API Top 10 检测 - 覆盖最新API安全风险",
                "业务逻辑漏洞检测 - 针对电商业务的专项检测",
                "权限控制检测 - 水平和垂直越权检测",
                "数据泄露检测 - 敏感信息过度透出检测",
                "注入攻击检测 - SQL、XSS、命令注入等",
                "智能Fuzz测试 - 基于参数特征的智能载荷生成"
            ],
            "coverage": "多维度全方位安全检测"
        },
        
        "🧠 智能分析能力": {
            "methods": [
                "LLM风险评估 - 基于大模型的智能风险分析",
                "漏洞关联分析 - 识别攻击链和复合攻击",
                "业务影响分析 - 评估对业务的实际影响",
                "修复建议生成 - 提供具体可操作的修复方案",
                "攻击场景构建 - 模拟真实攻击路径"
            ],
            "coverage": "AI驱动的深度安全洞察"
        },
        
        "🎭 对抗防护能力": {
            "methods": [
                "设备指纹伪装 - 模拟真实设备特征",
                "行为模式模拟 - 模拟正常用户行为",
                "网络环境伪装 - IP轮换和代理池",
                "反检测技术 - 绕过WAF和风控系统",
                "动态策略调整 - 根据目标系统调整检测策略"
            ],
            "coverage": "强大的反检测和对抗能力"
        }
    }
    
    for capability_name, capability_info in capabilities.items():
        print(f"{capability_name}")
        print("-" * 60)
        for method in capability_info['methods']:
            print(f"  • {method}")
        print(f"\n  📊 覆盖范围: {capability_info['coverage']}")
        print()


async def demo_deployment_architecture():
    """演示：部署架构"""
    print("=== 部署架构演示 ===")
    print()
    
    deployment_info = {
        "架构模式": "微服务 + 容器化部署",
        "容器编排": "Kubernetes",
        "服务网格": "Istio",
        "负载均衡": "Nginx + HAProxy",
        "数据存储": "PostgreSQL + Redis + Elasticsearch",
        "消息队列": "RabbitMQ + Kafka",
        "监控体系": "Prometheus + Grafana + ELK",
        "CI/CD": "GitLab CI + ArgoCD"
    }
    
    print("🏗️ 部署架构信息:")
    for key, value in deployment_info.items():
        print(f"  {key}: {value}")
    print()
    
    # 服务清单
    services = {
        "核心服务": [
            "api-discovery-service (API资产发现服务)",
            "security-detection-service (安全检测服务)",
            "risk-analysis-service (风险分析服务)",
            "llm-intelligence-service (LLM智能分析服务)"
        ],
        "基础服务": [
            "api-gateway (API网关)",
            "auth-service (认证服务)",
            "config-service (配置服务)",
            "notification-service (通知服务)"
        ],
        "数据服务": [
            "postgresql (主数据库)",
            "redis (缓存数据库)",
            "elasticsearch (搜索引擎)",
            "minio (对象存储)"
        ],
        "监控服务": [
            "prometheus (指标收集)",
            "grafana (可视化监控)",
            "jaeger (链路追踪)",
            "alertmanager (告警管理)"
        ]
    }
    
    print("🔧 服务组件清单:")
    for category, service_list in services.items():
        print(f"\n  {category}:")
        for service in service_list:
            print(f"    • {service}")
    
    print("\n" + "=" * 60)
    print("🚀 部署特性:")
    print("  ✓ 高可用性 - 多实例部署，故障自动切换")
    print("  ✓ 弹性伸缩 - 根据负载自动扩缩容")
    print("  ✓ 滚动更新 - 零停机时间更新")
    print("  ✓ 服务发现 - 自动服务注册和发现")
    print("  ✓ 配置管理 - 集中化配置管理")
    print("  ✓ 安全隔离 - 网络策略和RBAC权限控制")


async def main():
    """主函数"""
    print("淘宝API安全检测Agent - 企业级架构演示")
    print("=" * 80)
    print()
    
    try:
        # 1. 架构概览
        await demo_enterprise_architecture_overview()
        print("\n" + "=" * 80 + "\n")
        
        # 2. 系统能力展示
        await demo_system_capabilities()
        print("\n" + "=" * 80 + "\n")
        
        # 3. API资产发现演示
        await demo_api_asset_discovery()
        print("\n" + "=" * 80 + "\n")
        
        # 4. 部署架构演示
        await demo_deployment_architecture()
        
        print("\n" + "=" * 80)
        print("🎉 企业级架构演示完成！")
        print("\n💡 关键价值:")
        print("  • 企业级的可扩展架构设计")
        print("  • 全面的API安全检测能力")
        print("  • 智能化的风险分析和报告")
        print("  • 强大的反检测对抗技术")
        print("  • 完整的DevOps和监控体系")
        
    except Exception as e:
        print(f"演示运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
