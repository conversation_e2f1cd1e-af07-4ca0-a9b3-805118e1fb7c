# API安全Fuzz测试策略

## 概述

本文档详细介绍了淘宝API安全检测Agent中的智能Fuzz测试策略，包括参数选择原则、风险维度分析和载荷生成方法。

## 1. 参数选择策略

### 1.1 参数类型识别

我们将API参数分为以下类型，每种类型有不同的安全风险：

#### 高风险参数类型
- **ID类参数** (`userId`, `orderId`, `itemId`)
  - 风险：越权访问、数据泄露
  - 特征：包含`id`、`uid`等关键词
  - 优先级：⭐⭐⭐⭐⭐

- **Token类参数** (`token`, `authKey`, `secret`)
  - 风险：身份认证绕过
  - 特征：包含`token`、`key`、`auth`等关键词
  - 优先级：⭐⭐⭐⭐⭐

- **SQL相关参数** (包含SQL关键词的参数)
  - 风险：SQL注入攻击
  - 特征：值中包含`select`、`insert`等SQL关键词
  - 优先级：⭐⭐⭐⭐⭐

#### 中风险参数类型
- **URL参数** (`callback`, `redirect`, `url`)
  - 风险：SSRF攻击、开放重定向
  - 特征：包含`url`、`link`、`href`或以`http`开头的值
  - 优先级：⭐⭐⭐⭐

- **路径参数** (`file`, `path`, `dir`)
  - 风险：路径遍历、文件包含
  - 特征：包含`path`、`file`或值中包含`/`
  - 优先级：⭐⭐⭐⭐

- **邮箱/手机参数** (`email`, `phone`, `mobile`)
  - 风险：敏感信息泄露、输入验证绕过
  - 特征：符合邮箱或手机号格式
  - 优先级：⭐⭐⭐

#### 低风险参数类型
- **数值参数** (`page`, `size`, `count`)
  - 风险：边界值攻击、业务逻辑绕过
  - 优先级：⭐⭐

- **字符串参数** (普通文本参数)
  - 风险：XSS、输入验证问题
  - 优先级：⭐

### 1.2 参数敏感度评分算法

```python
def calculate_sensitivity_score(param_name, param_value, param_type, api_info):
    score = 0.0
    
    # 基础分数（基于参数类型）
    type_scores = {
        'ID': 8.0, 'TOKEN': 9.0, 'EMAIL': 7.0, 'PHONE': 7.0,
        'URL': 6.0, 'PATH': 7.0, 'SQL': 9.0, 'NUMERIC': 4.0, 'STRING': 3.0
    }
    score += type_scores.get(param_type, 3.0)
    
    # 敏感关键词加分
    sensitive_keywords = {
        'password': 2.0, 'admin': 1.5, 'token': 1.5, 'id': 1.0
    }
    for keyword, bonus in sensitive_keywords.items():
        if keyword in param_name.lower():
            score += bonus
    
    # API路径上下文加分
    if 'admin' in api_info.path.lower():
        score += 1.0
    
    return min(max(score, 0.0), 10.0)
```

## 2. 风险维度分析

### 2.1 核心风险维度

#### 🔴 注入攻击 (Injection)
**目标参数**：所有用户输入参数，特别是：
- 查询参数 (`q`, `search`, `keyword`)
- JSON/XML格式参数
- 路径参数

**测试载荷**：
```python
sql_injection_payloads = [
    "' OR '1'='1",
    "'; DROP TABLE users; --",
    "' UNION SELECT * FROM users --",
    "1' AND (SELECT COUNT(*) FROM users) > 0 --"
]

xss_payloads = [
    "<script>alert('XSS')</script>",
    "javascript:alert(1)",
    "<img src=x onerror=alert(1)>",
    "';alert(String.fromCharCode(88,83,83))//'"
]

command_injection_payloads = [
    "; cat /etc/passwd",
    "| whoami",
    "`id`",
    "$(uname -a)"
]
```

#### 🟠 权限控制 (Authorization)
**目标参数**：ID类参数和权限相关参数
- `userId`, `orderId`, `itemId`
- `role`, `permission`, `level`

**测试策略**：
```python
authorization_tests = [
    # 水平越权测试
    {'original_id': '12345', 'test_ids': ['1', '999999', '0', '-1']},
    
    # 垂直越权测试
    {'role_escalation': ['admin', 'root', 'super', 'manager']},
    
    # ID枚举测试
    {'id_enumeration': range(1, 1000)},
    
    # 特殊值测试
    {'special_values': ['null', 'undefined', '', 'admin']}
]
```

#### 🟡 数据泄露 (Data Exposure)
**目标参数**：可能影响响应内容的参数
- `fields`, `select`, `include`
- `format`, `output`, `export`

**测试方法**：
```python
data_exposure_tests = [
    # 字段操作测试
    {'fields': ['*', 'password', 'secret', 'token', 'all']},
    
    # 格式操作测试
    {'format': ['json', 'xml', 'debug', 'raw', 'full']},
    
    # 参数添加测试
    {'additional_params': {
        'debug': 'true',
        'verbose': '1',
        'admin': 'true',
        'internal': '1'
    }}
]
```

#### 🔵 业务逻辑 (Business Logic)
**目标参数**：业务相关的数值参数
- `price`, `amount`, `quantity`
- `discount`, `coupon`, `points`

**测试载荷**：
```python
business_logic_tests = [
    # 价格操作测试
    {'price_tests': [-1, 0, 0.01, 999999999, 'free']},
    
    # 数量操作测试
    {'quantity_tests': [-1, 0, 999999, 1.5, 'unlimited']},
    
    # 折扣操作测试
    {'discount_tests': [-100, 0, 100, 101, 999, '100%']},
    
    # 时间操作测试
    {'time_tests': ['1970-01-01', '2099-12-31', 'now', 'null']}
]
```

### 2.2 风险维度优先级矩阵

| 参数类型 | 注入攻击 | 权限控制 | 数据泄露 | 业务逻辑 | 输入验证 |
|---------|---------|---------|---------|---------|---------|
| ID类     | ⭐⭐     | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐   | ⭐⭐⭐     | ⭐⭐     |
| Token类  | ⭐       | ⭐⭐⭐⭐⭐ | ⭐⭐⭐     | ⭐       | ⭐⭐⭐   |
| 数值类   | ⭐⭐     | ⭐⭐     | ⭐       | ⭐⭐⭐⭐⭐ | ⭐⭐⭐   |
| 字符串类 | ⭐⭐⭐⭐   | ⭐       | ⭐⭐     | ⭐⭐     | ⭐⭐⭐⭐ |
| URL类    | ⭐⭐⭐   | ⭐       | ⭐       | ⭐       | ⭐⭐⭐⭐ |

## 3. 智能载荷生成

### 3.1 上下文感知载荷生成

根据API的业务上下文生成针对性载荷：

#### 电商场景载荷
```python
ecommerce_payloads = {
    'user_profile': {
        'userId': ['0', '-1', '999999', 'admin', 'null'],
        'fields': ['password', 'secret', 'admin', '*']
    },
    'product_search': {
        'keyword': ["' OR 1=1 --", '<script>alert(1)</script>'],
        'category': ['../../../etc/passwd', 'admin']
    },
    'order_management': {
        'orderId': ['0', 'admin_order', '999999'],
        'status': ['cancelled', 'refunded', 'admin']
    }
}
```

#### 管理后台载荷
```python
admin_payloads = {
    'user_management': {
        'role': ['admin', 'super', 'root'],
        'permission': ['all', '*', 'admin']
    },
    'system_config': {
        'config_key': ['database_password', 'secret_key'],
        'value': ['$(cat /etc/passwd)', '{{7*7}}']
    }
}
```

### 3.2 载荷优先级算法

```python
def calculate_payload_priority(payload_type, param_profile, api_context):
    priority = 0
    
    # 基于载荷类型的基础优先级
    base_priorities = {
        'sql_injection': 9,
        'command_injection': 9,
        'id_manipulation': 8,
        'xss': 7,
        'business_logic': 7,
        'input_validation': 5
    }
    priority += base_priorities.get(payload_type, 3)
    
    # 基于参数敏感度的调整
    if param_profile.sensitivity_score >= 8:
        priority += 2
    elif param_profile.sensitivity_score >= 6:
        priority += 1
    
    # 基于API上下文的调整
    if 'admin' in api_context.path:
        priority += 1
    
    return min(priority, 10)
```

## 4. 实际应用示例

### 4.1 淘宝用户资料接口分析

**API**: `GET /api/user/profile?userId=12345&fields=basic`

**参数分析**：
```python
parameters = {
    'userId': {
        'type': 'ID',
        'sensitivity': 8.0,
        'risk_dimensions': ['AUTHORIZATION', 'DATA_EXPOSURE'],
        'priority': 5
    },
    'fields': {
        'type': 'STRING',
        'sensitivity': 6.0,
        'risk_dimensions': ['DATA_EXPOSURE', 'INPUT_VALIDATION'],
        'priority': 4
    }
}
```

**生成的测试用例**：
```python
test_cases = [
    # userId参数测试
    {'userId': '0', 'fields': 'basic'},           # ID为0测试
    {'userId': '-1', 'fields': 'basic'},          # 负数ID测试
    {'userId': '999999', 'fields': 'basic'},      # 大数ID测试
    {'userId': 'admin', 'fields': 'basic'},       # 字符串ID测试
    
    # fields参数测试
    {'userId': '12345', 'fields': '*'},           # 通配符测试
    {'userId': '12345', 'fields': 'password'},    # 敏感字段测试
    {'userId': '12345', 'fields': 'admin'},       # 权限字段测试
    {'userId': '12345', 'fields': 'all'},         # 全部字段测试
    
    # 组合测试
    {'userId': '0', 'fields': '*'},               # 高风险组合
    {'userId': 'admin', 'fields': 'password'},    # 极高风险组合
]
```

### 4.2 测试结果分析

**响应差异检测**：
```python
def analyze_response_differences(original_response, test_response):
    risks = []
    
    # 状态码差异
    if test_response.status_code != original_response.status_code:
        risks.append({
            'type': 'status_code_change',
            'risk_level': 'medium',
            'description': f'状态码从{original_response.status_code}变为{test_response.status_code}'
        })
    
    # 响应大小差异
    size_diff = len(test_response.text) - len(original_response.text)
    if abs(size_diff) > 100:
        risks.append({
            'type': 'response_size_change',
            'risk_level': 'low',
            'description': f'响应大小变化{size_diff}字节'
        })
    
    # 敏感信息检测
    sensitive_patterns = [
        r'password', r'secret', r'token', r'admin',
        r'\d{11}', r'\w+@\w+\.\w+', r'\d{15,19}'
    ]
    
    for pattern in sensitive_patterns:
        if re.search(pattern, test_response.text, re.I):
            risks.append({
                'type': 'sensitive_data_exposure',
                'risk_level': 'high',
                'description': f'检测到敏感信息模式: {pattern}'
            })
    
    return risks
```

## 5. 最佳实践建议

### 5.1 Fuzz测试流程

1. **参数画像分析** - 识别参数类型和敏感度
2. **风险维度映射** - 确定需要测试的风险维度
3. **载荷智能生成** - 基于上下文生成针对性载荷
4. **优先级排序** - 按风险评分排序测试用例
5. **响应差异分析** - 检测异常响应和敏感信息
6. **结果智能分析** - 使用LLM分析测试结果

### 5.2 性能优化建议

- **并发控制**: 限制并发请求数量，避免触发防护机制
- **频率控制**: 控制请求频率，模拟正常用户行为
- **载荷限制**: 每个参数限制测试载荷数量，提高效率
- **智能跳过**: 基于响应模式智能跳过无效测试

### 5.3 误报减少策略

- **基线建立**: 建立正常响应的基线模式
- **上下文分析**: 结合API业务上下文分析结果
- **多维度验证**: 从多个维度验证安全问题
- **人工确认**: 高风险问题需要人工二次确认

这套智能Fuzz策略能够显著提高API安全测试的效率和准确性，特别适用于大型电商平台的API安全检测。
