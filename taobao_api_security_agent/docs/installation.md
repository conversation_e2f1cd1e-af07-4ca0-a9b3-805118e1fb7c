# 安装指南

本文档介绍如何安装和配置淘宝API安全检测Agent。

## 系统要求

- Python 3.8 或更高版本
- 操作系统：Windows、macOS、Linux
- 内存：建议 4GB 以上
- 磁盘空间：至少 1GB 可用空间

## 安装步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd taobao_api_security_agent
```

### 2. 创建虚拟环境

```bash
# 使用 venv
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入实际的配置值
nano .env  # 或使用其他编辑器
```

### 5. 配置文件设置

配置文件位于 `config/` 目录下，包含以下文件：

- `agent_config.yaml` - Agent主配置
- `llm_config.yaml` - 大模型配置
- `signature_config.yaml` - 签名服务配置
- `session_config.yaml` - 会话配置

根据实际需求修改这些配置文件。

## 必需的配置

### LLM API 密钥

至少需要配置一个LLM提供商的API密钥：

```bash
# 在 .env 文件中设置
ANTHROPIC_API_KEY=your_anthropic_api_key
# 或
GOOGLE_API_KEY=your_google_api_key
# 或
DASHSCOPE_API_KEY=your_dashscope_api_key
```

### 淘宝相关配置

如果需要使用签名服务和会话管理功能，需要配置：

```bash
# 签名服务
TAOBAO_SIGNATURE_SERVICE_URL=your_signature_service_url
SIGNATURE_SERVICE_API_KEY=your_api_key

# 测试用户会话
TEST_USER_NORMAL_TB_TOKEN=your_tb_token
# ... 其他Cookie配置
```

## 验证安装

### 1. 运行基本测试

```bash
python -c "from src.agent.main_agent import TaobaoAPISecurityAgent; print('安装成功！')"
```

### 2. 运行示例

```bash
python examples/usage_examples.py
```

### 3. 检查配置

```bash
python -c "
from src.utils.config_loader import get_config_loader
config_loader = get_config_loader()
configs = config_loader.load_all_configs()
print(f'加载了 {len(configs)} 个配置文件')
"
```

## 可选组件

### 1. 数据库支持

如果需要持久化存储，可以配置数据库：

```bash
# PostgreSQL
pip install psycopg2-binary
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# SQLite (默认)
DATABASE_URL=sqlite:///./data/agent.db
```

### 2. Redis缓存

用于提高性能的缓存支持：

```bash
pip install redis
REDIS_PASSWORD=your_redis_password
```

### 3. PCAP文件解析

如果需要解析PCAP文件：

```bash
pip install scapy
```

## 目录结构

安装完成后的目录结构：

```
taobao_api_security_agent/
├── config/                 # 配置文件
├── src/                    # 源代码
├── examples/               # 使用示例
├── docs/                   # 文档
├── tests/                  # 测试文件
├── logs/                   # 日志文件 (运行时创建)
├── reports/                # 报告输出 (运行时创建)
├── .env                    # 环境变量配置
└── requirements.txt        # Python依赖
```

## 常见问题

### Q: 安装依赖时出现错误

A: 确保使用正确的Python版本，并尝试升级pip：
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### Q: 配置文件加载失败

A: 检查配置文件格式是否正确，确保YAML语法无误：
```bash
python -c "import yaml; yaml.safe_load(open('config/agent_config.yaml'))"
```

### Q: LLM API调用失败

A: 检查API密钥是否正确设置，网络连接是否正常：
```bash
# 测试API连接
python -c "
import os
print('ANTHROPIC_API_KEY:', 'SET' if os.getenv('ANTHROPIC_API_KEY') else 'NOT SET')
"
```

### Q: 权限错误

A: 确保有足够的权限创建日志和报告目录：
```bash
mkdir -p logs reports
chmod 755 logs reports
```

## 下一步

安装完成后，请参考以下文档：

- [配置说明](configuration.md) - 详细的配置选项说明
- [使用指南](../examples/usage_examples.py) - 使用示例和最佳实践
- [API参考](api_reference.md) - 完整的API文档

## 获取帮助

如果遇到问题，可以：

1. 查看日志文件：`logs/agent.log`
2. 运行调试模式：设置环境变量 `DEBUG=true`
3. 查看详细错误信息：使用 `--verbose` 参数

## 更新

更新到最新版本：

```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

注意：更新前请备份配置文件和数据。
