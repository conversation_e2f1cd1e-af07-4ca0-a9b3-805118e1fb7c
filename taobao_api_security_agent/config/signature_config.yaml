# 淘宝签名服务配置文件

# 签名服务基础配置
signature_service:
  # 服务基础信息
  name: "TaobaoSignatureService"
  version: "1.0"
  
  # 服务端点配置
  endpoints:
    # 主签名服务
    primary:
      url: "${TAOBAO_SIGNATURE_SERVICE_URL}"  # 从环境变量读取
      timeout: 10
      max_retries: 3
      retry_delay: 1  # 秒
    
    # 备用签名服务
    fallback:
      url: "${TAOBAO_SIGNATURE_SERVICE_FALLBACK_URL}"
      timeout: 15
      max_retries: 2
      retry_delay: 2

  # 认证配置
  authentication:
    # API密钥
    api_key: "${SIGNATURE_SERVICE_API_KEY}"
    
    # 认证方式: header, query, body
    auth_method: "header"
    
    # 认证头名称
    auth_header: "X-API-Key"
    
    # 是否启用签名验证
    verify_signature: true

# 签名算法配置
signature_algorithms:
  # 默认算法
  default: "hmac_sha256"
  
  # 支持的算法列表
  supported:
    - name: "hmac_sha256"
      description: "HMAC-SHA256签名算法"
      parameters:
        secret_key: "${TAOBAO_SECRET_KEY}"
        encoding: "utf-8"
    
    - name: "md5"
      description: "MD5签名算法"
      parameters:
        secret_key: "${TAOBAO_MD5_KEY}"
        encoding: "utf-8"
    
    - name: "custom_taobao"
      description: "淘宝自定义签名算法"
      parameters:
        app_key: "${TAOBAO_APP_KEY}"
        app_secret: "${TAOBAO_APP_SECRET}"
        version: "2.0"

# 签名参数配置
signature_parameters:
  # 必需参数
  required_params:
    - "timestamp"
    - "api"
    - "v"
    - "sign_method"
    - "app_key"
  
  # 可选参数
  optional_params:
    - "format"
    - "simplify"
    - "callback"
  
  # 系统参数
  system_params:
    timestamp:
      auto_generate: true
      format: "unix"  # unix, iso8601
    
    api:
      auto_detect: true
      fallback: "taobao.api.default"
    
    v:
      default: "2.0"
    
    sign_method:
      default: "hmac"
    
    app_key:
      source: "config"  # config, env, runtime
    
    format:
      default: "json"

# 请求重构配置
request_reconstruction:
  # 是否启用请求重构
  enabled: true
  
  # 重构策略
  strategy:
    # 参数排序
    sort_params: true
    
    # URL编码
    url_encode: true
    
    # 移除空参数
    remove_empty_params: true
    
    # 参数名大小写处理: preserve, lower, upper
    param_case: "preserve"
  
  # 头部处理
  headers:
    # 需要保留的头部
    preserve_headers:
      - "User-Agent"
      - "Referer"
      - "Cookie"
      - "X-Requested-With"
    
    # 需要移除的头部
    remove_headers:
      - "X-Forwarded-For"
      - "X-Real-IP"
    
    # 需要更新的头部
    update_headers:
      "Content-Type": "application/x-www-form-urlencoded"

# 缓存配置
cache:
  # 是否启用签名缓存
  enabled: true
  
  # 缓存类型: memory, redis, file
  type: "memory"
  
  # 缓存过期时间(秒)
  ttl: 300
  
  # 最大缓存条目数
  max_entries: 1000
  
  # Redis配置(当type为redis时)
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: "${REDIS_PASSWORD}"

# 错误处理配置
error_handling:
  # 签名失败时的处理策略
  on_signature_failure:
    # 策略: retry, skip, fail
    strategy: "retry"
    
    # 最大重试次数
    max_retries: 3
    
    # 重试间隔(秒)
    retry_interval: 2
    
    # 是否尝试备用算法
    try_fallback_algorithm: true
  
  # 服务不可用时的处理
  on_service_unavailable:
    strategy: "skip"  # skip, fail, use_cached
    
    # 使用缓存的签名
    use_cached_signature: true
    
    # 缓存签名的最大年龄(秒)
    max_cached_age: 3600

# 监控和日志配置
monitoring:
  # 是否启用性能监控
  enabled: true
  
  # 监控指标
  metrics:
    - "signature_generation_time"
    - "api_call_success_rate"
    - "cache_hit_rate"
    - "error_rate"
  
  # 日志级别
  log_level: "INFO"
  
  # 是否记录敏感信息
  log_sensitive_data: false

# 安全配置
security:
  # 密钥轮换
  key_rotation:
    enabled: false
    interval_days: 30
    
  # 签名验证
  signature_verification:
    enabled: true
    tolerance_seconds: 300  # 时间戳容差
  
  # IP白名单
  ip_whitelist:
    enabled: false
    allowed_ips: []
  
  # 请求频率限制
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst_size: 20

# 调试配置
debug:
  # 是否启用调试模式
  enabled: false
  
  # 调试输出
  output:
    # 是否输出原始请求
    show_raw_request: true
    
    # 是否输出签名过程
    show_signature_process: true
    
    # 是否输出重构后的请求
    show_reconstructed_request: true
  
  # 调试日志文件
  log_file: "./logs/signature_debug.log"
