# 淘宝API安全检测Agent主配置文件

# Agent基础配置
agent:
  name: "TaobaoAPISecurityAgent"
  version: "1.0.0"
  description: "专业的淘宝API安全检测Agent"
  
  # 工作模式配置
  mode:
    # 检测模式: comprehensive(全面), fast(快速), custom(自定义)
    detection_mode: "comprehensive"
    # 并发请求数
    max_concurrent_requests: 10
    # 请求超时时间(秒)
    request_timeout: 30
    # 重试次数
    max_retries: 3

# 目标域名配置
targets:
  # 主域名
  primary_domain: "taobao.com"
  
  # 子域名列表
  subdomains:
    - "www.taobao.com"
    - "main.m.taobao.com"
    - "h5.m.taobao.com"
    - "cart.taobao.com"
    - "trade.taobao.com"
    - "member1.taobao.com"
    - "login.taobao.com"
    - "passport.taobao.com"
    - "favorite.taobao.com"
    - "shoucang.taobao.com"
  
  # 排除的路径模式
  exclude_patterns:
    - "*/static/*"
    - "*/assets/*"
    - "*/images/*"
    - "*/css/*"
    - "*/js/*"

# 安全检测配置
security_checks:
  # 字段过度透出检测
  field_exposure:
    enabled: true
    # 敏感字段关键词
    sensitive_keywords:
      - "password"
      - "token"
      - "secret"
      - "key"
      - "phone"
      - "mobile"
      - "email"
      - "address"
      - "idcard"
      - "bankcard"
      - "realname"
    # 检测深度
    max_depth: 5
  
  # 越权访问检测
  privilege_escalation:
    enabled: true
    # 测试用户ID范围
    test_user_ids:
      - "test_user_1"
      - "test_user_2"
      - "anonymous"
    # 权限测试类型
    test_types:
      - "horizontal"  # 水平越权
      - "vertical"    # 垂直越权
  
  # 反爬策略检测
  anti_crawler:
    enabled: true
    # 检测项目
    checks:
      - "rate_limiting"     # 频率限制
      - "user_agent"        # User-Agent检查
      - "captcha"          # 验证码
      - "ip_blocking"      # IP封禁
      - "behavior_analysis" # 行为分析
  
  # OWASP API Security Top 10
  owasp_fuzzer:
    enabled: true
    # 启用的检测项
    enabled_checks:
      - "API1_2023_BOLA"           # 对象级授权失效
      - "API2_2023_BROKEN_AUTH"    # 身份验证失效
      - "API3_2023_BROKEN_OBJ_PROP" # 对象属性级授权失效
      - "API4_2023_UNRESTRICTED"   # 无限制资源消耗
      - "API5_2023_BFLA"           # 功能级授权失效
      - "API6_2023_UNRESTRICTED_ACCESS" # 无限制访问敏感业务流
      - "API7_2023_SERVER_SIDE_REQUEST_FORGERY" # 服务端请求伪造
      - "API8_2023_SECURITY_MISCONFIGURATION" # 安全配置错误
      - "API9_2023_IMPROPER_INVENTORY_MANAGEMENT" # 不当的库存管理
      - "API10_2023_UNSAFE_CONSUMPTION" # 不安全的API消费

# 输入处理配置
input_processing:
  # 支持的输入格式
  supported_formats:
    - "har"           # HTTP Archive格式
    - "pcap"          # 网络包捕获格式
    - "json"          # JSON格式API列表
    - "csv"           # CSV格式API列表
    - "txt"           # 纯文本格式
    - "xml"           # XML格式
  
  # 数据预处理
  preprocessing:
    # 去重
    deduplicate: true
    # 数据清洗
    clean_data: true
    # 格式标准化
    normalize: true

# 输出配置
output:
  # 报告格式
  report_formats:
    - "json"
    - "html"
    - "pdf"
    - "csv"
  
  # 输出目录
  output_dir: "./reports"
  
  # 报告详细程度: minimal, standard, detailed
  detail_level: "detailed"
  
  # 是否包含原始数据
  include_raw_data: false

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/agent.log"
  max_size: "10MB"
  backup_count: 5

# 性能配置
performance:
  # 内存限制(MB)
  memory_limit: 1024
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600  # 缓存过期时间(秒)
    max_size: 1000  # 最大缓存条目数

# 智能分析配置
intelligent_analysis:
  enabled: true

  # 分析配置
  temperature: 0.1
  max_context_length: 8000
  batch_size: 5

  # 分析类型
  analysis_types:
    risk_assessment: true
    vulnerability_correlation: true
    remediation_suggestions: true
    business_impact: true
    attack_scenarios: true
    behavior_analysis: true
    comprehensive_summary: true

  # 提示词配置
  custom_prompts:
    enabled: false
    prompt_file: "custom_prompts.yaml"

# 报告生成配置
reporting:
  enabled: true

  # 输出配置
  output_dir: "reports"
  template_dir: "templates"

  # 报告格式
  formats:
    html: true
    json: true
    markdown: true
    pdf: false  # 需要额外依赖

  # 报告内容
  include_raw_data: false
  include_charts: true
  generate_charts: true
  pretty_print: true
  compress_output: false

  # 报告主题
  theme: "default"

  # 自定义配置
  report_title: "淘宝API安全评估报告"
  company_name: "API安全检测团队"
  logo_path: ""
