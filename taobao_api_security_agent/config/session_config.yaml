# 会话管理配置文件

# 会话管理基础配置
session_manager:
  name: "TaobaoSessionManager"
  version: "1.0"
  
  # 会话存储配置
  storage:
    # 存储类型: file, redis, database, memory
    type: "file"
    
    # 文件存储配置
    file:
      path: "./config/sessions/"
      format: "json"  # json, yaml, encrypted
      auto_backup: true
      backup_interval: 3600  # 秒
    
    # Redis存储配置
    redis:
      host: "localhost"
      port: 6379
      db: 1
      password: "${REDIS_PASSWORD}"
      key_prefix: "taobao_session:"
    
    # 数据库存储配置
    database:
      url: "${DATABASE_URL}"
      table_name: "user_sessions"

# 用户会话配置
user_sessions:
  # 测试用户会话
  test_users:
    # 普通用户
    - user_id: "test_user_normal"
      username: "test_normal"
      user_type: "normal"
      cookies:
        # 淘宝主要Cookie
        "_tb_token_": "${TEST_USER_NORMAL_TB_TOKEN}"
        "cookie2": "${TEST_USER_NORMAL_COOKIE2}"
        "t": "${TEST_USER_NORMAL_T}"
        "cna": "${TEST_USER_NORMAL_CNA}"
        "isg": "${TEST_USER_NORMAL_ISG}"
        "l": "${TEST_USER_NORMAL_L}"
        "thw": "${TEST_USER_NORMAL_THW}"
      
      headers:
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
        "Referer": "https://main.m.taobao.com/"
        "X-Requested-With": "XMLHttpRequest"
      
      # 会话有效期
      expires_at: "2024-12-31T23:59:59Z"
      
      # 权限级别
      permission_level: "user"
    
    # VIP用户
    - user_id: "test_user_vip"
      username: "test_vip"
      user_type: "vip"
      cookies:
        "_tb_token_": "${TEST_USER_VIP_TB_TOKEN}"
        "cookie2": "${TEST_USER_VIP_COOKIE2}"
        "t": "${TEST_USER_VIP_T}"
        "cna": "${TEST_USER_VIP_CNA}"
        "isg": "${TEST_USER_VIP_ISG}"
        "l": "${TEST_USER_VIP_L}"
        "thw": "${TEST_USER_VIP_THW}"
      
      headers:
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
        "Referer": "https://www.taobao.com/"
        "X-Requested-With": "XMLHttpRequest"
      
      expires_at: "2024-12-31T23:59:59Z"
      permission_level: "vip"
    
    # 商家用户
    - user_id: "test_user_seller"
      username: "test_seller"
      user_type: "seller"
      cookies:
        "_tb_token_": "${TEST_USER_SELLER_TB_TOKEN}"
        "cookie2": "${TEST_USER_SELLER_COOKIE2}"
        "t": "${TEST_USER_SELLER_T}"
        "cna": "${TEST_USER_SELLER_CNA}"
        "isg": "${TEST_USER_SELLER_ISG}"
        "l": "${TEST_USER_SELLER_L}"
        "thw": "${TEST_USER_SELLER_THW}"
      
      headers:
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        "Referer": "https://seller.taobao.com/"
        "X-Requested-With": "XMLHttpRequest"
      
      expires_at: "2024-12-31T23:59:59Z"
      permission_level: "seller"
  
  # 匿名会话
  anonymous_session:
    enabled: true
    cookies:
      "cna": "${ANONYMOUS_CNA}"
      "isg": "${ANONYMOUS_ISG}"
      "thw": "${ANONYMOUS_THW}"
    
    headers:
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      "Referer": "https://www.taobao.com/"

# 会话验证配置
session_validation:
  # 是否启用会话验证
  enabled: true
  
  # 验证策略
  validation_strategy:
    # 验证间隔(秒)
    check_interval: 300
    
    # 验证API端点
    validation_endpoints:
      - url: "https://login.taobao.com/member/login_unusual.htm"
        method: "GET"
        expected_status: [200, 302]
      
      - url: "https://main.m.taobao.com/api/user/info"
        method: "GET"
        expected_status: [200]
        expected_response_contains: ["nick", "userId"]
  
  # 会话刷新配置
  refresh_strategy:
    # 自动刷新
    auto_refresh: true
    
    # 刷新阈值(剩余有效时间小于此值时刷新)
    refresh_threshold: 3600  # 秒
    
    # 刷新方法
    refresh_method: "api_call"  # api_call, cookie_update, re_login

# 会话轮换配置
session_rotation:
  # 是否启用会话轮换
  enabled: true
  
  # 轮换策略
  rotation_strategy:
    # 轮换方式: round_robin, random, weighted
    method: "round_robin"
    
    # 轮换间隔(请求数)
    interval: 10
    
    # 权重配置(当method为weighted时)
    weights:
      normal: 1
      vip: 2
      seller: 1
  
  # 会话健康检查
  health_check:
    enabled: true
    check_interval: 60  # 秒
    unhealthy_threshold: 3  # 连续失败次数

# 代理配置
proxy:
  # 是否启用代理
  enabled: false
  
  # 代理列表
  proxies:
    - type: "http"
      host: "proxy1.example.com"
      port: 8080
      username: "${PROXY_USERNAME}"
      password: "${PROXY_PASSWORD}"
    
    - type: "socks5"
      host: "proxy2.example.com"
      port: 1080
  
  # 代理轮换
  rotation:
    enabled: true
    method: "random"

# 错误处理配置
error_handling:
  # 会话失效处理
  on_session_invalid:
    # 处理策略: retry, switch, fail
    strategy: "switch"
    
    # 最大重试次数
    max_retries: 3
    
    # 重试间隔(秒)
    retry_interval: 5
  
  # 网络错误处理
  on_network_error:
    strategy: "retry"
    max_retries: 3
    retry_interval: 2
    
    # 指数退避
    exponential_backoff: true
    max_backoff: 60

# 安全配置
security:
  # Cookie加密
  cookie_encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key: "${COOKIE_ENCRYPTION_KEY}"
  
  # 会话隔离
  session_isolation:
    enabled: true
    # 每个会话使用独立的请求客户端
    separate_clients: true
  
  # 敏感信息脱敏
  data_masking:
    enabled: true
    # 在日志中脱敏的字段
    mask_fields:
      - "cookie"
      - "token"
      - "password"
      - "secret"

# 监控配置
monitoring:
  # 会话使用统计
  usage_stats:
    enabled: true
    
    # 统计指标
    metrics:
      - "session_success_rate"
      - "session_response_time"
      - "session_error_count"
      - "session_rotation_count"
  
  # 告警配置
  alerts:
    # 会话失效率告警
    session_failure_rate:
      threshold: 0.1  # 10%
      window: 300     # 5分钟窗口
    
    # 响应时间告警
    response_time:
      threshold: 5000  # 5秒
      window: 60       # 1分钟窗口

# 调试配置
debug:
  enabled: false
  
  # 调试输出
  output:
    show_cookies: false
    show_headers: true
    show_response: false
  
  # 调试日志
  log_file: "./logs/session_debug.log"
