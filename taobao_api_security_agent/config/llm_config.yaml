# LLM大模型配置文件

# 默认使用的LLM提供商
default_provider: "claude"

# LLM提供商配置
providers:
  # Claude配置
  claude:
    enabled: true
    model: "claude-3-5-sonnet-20241022"
    api_key: "${ANTHROPIC_API_KEY}"  # 从环境变量读取
    base_url: "https://api.anthropic.com"
    max_tokens: 4096
    temperature: 0.1
    timeout: 60
    # 重试配置
    retry:
      max_attempts: 3
      backoff_factor: 2
    # 速率限制
    rate_limit:
      requests_per_minute: 50
      tokens_per_minute: 40000
  
  # Gemini Pro 2.5配置
  gemini:
    enabled: true
    model: "gemini-2.0-flash-exp"
    api_key: "${GOOGLE_API_KEY}"
    base_url: "https://generativelanguage.googleapis.com"
    max_tokens: 8192
    temperature: 0.1
    timeout: 60
    retry:
      max_attempts: 3
      backoff_factor: 2
    rate_limit:
      requests_per_minute: 60
      tokens_per_minute: 32000
  
  # Qwen配置
  qwen:
    enabled: true
    model: "qwen-max"
    api_key: "${DASHSCOPE_API_KEY}"
    base_url: "https://dashscope.aliyuncs.com/api/v1"
    max_tokens: 6000
    temperature: 0.1
    timeout: 60
    retry:
      max_attempts: 3
      backoff_factor: 2
    rate_limit:
      requests_per_minute: 100
      tokens_per_minute: 120000
  
  # OpenAI GPT配置 (备用)
  openai:
    enabled: false
    model: "gpt-4-turbo-preview"
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    max_tokens: 4096
    temperature: 0.1
    timeout: 60
    retry:
      max_attempts: 3
      backoff_factor: 2
    rate_limit:
      requests_per_minute: 50
      tokens_per_minute: 40000

# 任务特定的LLM配置
task_specific:
  # 安全分析任务
  security_analysis:
    preferred_provider: "claude"
    system_prompt: |
      你是一个专业的API安全分析专家，专门分析淘宝网站的API安全问题。
      请仔细分析提供的API请求和响应数据，识别潜在的安全风险。
      
      重点关注以下安全问题：
      1. 字段过度透出：检查响应中是否包含不应该暴露的敏感信息
      2. 越权访问：分析是否存在权限控制缺陷
      3. 反爬策略：评估API的反爬虫防护措施
      4. OWASP API Security Top 10相关漏洞
      
      请提供详细的分析结果，包括风险等级、具体问题描述和修复建议。
    
    temperature: 0.05  # 更低的温度确保分析的一致性
    max_tokens: 6000
  
  # 漏洞分类任务
  vulnerability_classification:
    preferred_provider: "gemini"
    system_prompt: |
      你是一个漏洞分类专家，需要对发现的API安全问题进行分类和优先级排序。
      
      请根据以下标准对漏洞进行分类：
      - 严重程度：Critical, High, Medium, Low
      - 漏洞类型：根据OWASP API Security Top 10分类
      - 影响范围：数据泄露、权限绕过、服务可用性等
      - 利用难度：Easy, Medium, Hard
      
      请提供结构化的分类结果。
    
    temperature: 0.1
    max_tokens: 3000
  
  # 报告生成任务
  report_generation:
    preferred_provider: "qwen"
    system_prompt: |
      你是一个专业的安全报告撰写专家，需要将技术分析结果转换为清晰易懂的安全报告。
      
      报告应该包括：
      1. 执行摘要
      2. 发现的安全问题详细描述
      3. 风险评估
      4. 修复建议
      5. 技术细节
      
      请使用专业但易懂的语言，确保技术和非技术人员都能理解。
    
    temperature: 0.2
    max_tokens: 8000

# 提示词模板
prompt_templates:
  # API安全分析模板
  api_security_analysis: |
    请分析以下API的安全性：
    
    API信息：
    - URL: {api_url}
    - 方法: {http_method}
    - 请求头: {request_headers}
    - 请求体: {request_body}
    - 响应状态: {response_status}
    - 响应头: {response_headers}
    - 响应体: {response_body}
    
    请重点检查：
    1. 是否存在敏感信息泄露
    2. 权限控制是否充分
    3. 输入验证是否完善
    4. 错误处理是否安全
    
    请提供JSON格式的分析结果。
  
  # 字段过度透出检测模板
  field_exposure_check: |
    请检查以下API响应是否存在字段过度透出问题：
    
    响应数据：
    {response_data}
    
    敏感字段关键词：
    {sensitive_keywords}
    
    请识别可能的敏感信息泄露，并评估风险等级。
  
  # 越权检测模板
  privilege_escalation_check: |
    请分析以下API调用是否存在越权访问问题：
    
    原始请求：
    {original_request}
    
    测试请求：
    {test_request}
    
    响应对比：
    原始响应：{original_response}
    测试响应：{test_response}
    
    请判断是否存在水平或垂直越权问题。

# 模型切换策略
fallback_strategy:
  # 启用自动切换
  enabled: true
  
  # 切换条件
  conditions:
    - condition: "rate_limit_exceeded"
      action: "switch_to_next_provider"
    - condition: "api_error"
      action: "retry_with_different_provider"
    - condition: "timeout"
      action: "switch_to_faster_provider"
  
  # 提供商优先级顺序
  priority_order:
    - "claude"
    - "gemini"
    - "qwen"
    - "openai"

# 成本控制
cost_control:
  # 每日最大token消耗
  daily_token_limit: 1000000
  
  # 单次请求最大token数
  max_tokens_per_request: 10000
  
  # 成本预警阈值
  cost_alert_threshold: 100  # 美元
  
  # 自动停止阈值
  auto_stop_threshold: 200   # 美元
