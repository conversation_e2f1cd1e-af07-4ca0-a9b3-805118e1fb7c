# 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# LLM API 密钥配置
# =============================================================================

# Anthropic Claude API密钥
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini API密钥
GOOGLE_API_KEY=your_google_api_key_here

# 阿里云DashScope API密钥 (Qwen)
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# OpenAI API密钥 (可选)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# 淘宝签名服务配置
# =============================================================================

# 淘宝签名服务URL
TAOBAO_SIGNATURE_SERVICE_URL=https://your-signature-service.com/api/sign

# 备用签名服务URL
TAOBAO_SIGNATURE_SERVICE_FALLBACK_URL=https://backup-signature-service.com/api/sign

# 签名服务API密钥
SIGNATURE_SERVICE_API_KEY=your_signature_service_api_key

# 淘宝应用密钥
TAOBAO_SECRET_KEY=your_taobao_secret_key
TAOBAO_MD5_KEY=your_taobao_md5_key
TAOBAO_APP_KEY=your_taobao_app_key
TAOBAO_APP_SECRET=your_taobao_app_secret

# =============================================================================
# 测试用户会话配置
# =============================================================================

# 普通用户会话Cookie
TEST_USER_NORMAL_TB_TOKEN=your_normal_user_tb_token
TEST_USER_NORMAL_COOKIE2=your_normal_user_cookie2
TEST_USER_NORMAL_T=your_normal_user_t_cookie
TEST_USER_NORMAL_CNA=your_normal_user_cna
TEST_USER_NORMAL_ISG=your_normal_user_isg
TEST_USER_NORMAL_L=your_normal_user_l_cookie
TEST_USER_NORMAL_THW=your_normal_user_thw

# VIP用户会话Cookie
TEST_USER_VIP_TB_TOKEN=your_vip_user_tb_token
TEST_USER_VIP_COOKIE2=your_vip_user_cookie2
TEST_USER_VIP_T=your_vip_user_t_cookie
TEST_USER_VIP_CNA=your_vip_user_cna
TEST_USER_VIP_ISG=your_vip_user_isg
TEST_USER_VIP_L=your_vip_user_l_cookie
TEST_USER_VIP_THW=your_vip_user_thw

# 商家用户会话Cookie
TEST_USER_SELLER_TB_TOKEN=your_seller_user_tb_token
TEST_USER_SELLER_COOKIE2=your_seller_user_cookie2
TEST_USER_SELLER_T=your_seller_user_t_cookie
TEST_USER_SELLER_CNA=your_seller_user_cna
TEST_USER_SELLER_ISG=your_seller_user_isg
TEST_USER_SELLER_L=your_seller_user_l_cookie
TEST_USER_SELLER_THW=your_seller_user_thw

# 匿名会话Cookie
ANONYMOUS_CNA=your_anonymous_cna
ANONYMOUS_ISG=your_anonymous_isg
ANONYMOUS_THW=your_anonymous_thw

# =============================================================================
# 数据库和缓存配置
# =============================================================================

# 数据库连接URL (可选)
DATABASE_URL=postgresql://user:password@localhost:5432/taobao_api_security

# Redis密码 (可选)
REDIS_PASSWORD=your_redis_password

# =============================================================================
# 安全配置
# =============================================================================

# Cookie加密密钥
COOKIE_ENCRYPTION_KEY=your_32_character_encryption_key_here

# =============================================================================
# 代理配置 (可选)
# =============================================================================

# 代理用户名和密码
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password

# =============================================================================
# 其他配置
# =============================================================================

# 调试模式
DEBUG=false

# 日志级别
LOG_LEVEL=INFO

# 最大并发请求数
MAX_CONCURRENT_REQUESTS=10

# 请求超时时间(秒)
REQUEST_TIMEOUT=30
