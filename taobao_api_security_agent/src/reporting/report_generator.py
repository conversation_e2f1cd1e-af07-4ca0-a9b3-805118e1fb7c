"""
智能报告生成器

使用LLM生成专业的安全评估报告。
"""

import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import logging

from ..agent.state import APIInfo, SecurityCheckResult
from ..integrations.llm_provider import LLMProvider
from ..security.intelligent_analyzer import IntelligentSecurityAnalyzer
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ReportGenerator:
    """智能报告生成器"""
    
    def __init__(
        self,
        llm_provider: LLMProvider,
        intelligent_analyzer: IntelligentSecurityAnalyzer,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化报告生成器
        
        Args:
            llm_provider: LLM提供商
            intelligent_analyzer: 智能分析器
            config: 配置信息
        """
        self.llm_provider = llm_provider
        self.intelligent_analyzer = intelligent_analyzer
        self.config = config or {}
        
        # 报告配置
        self.output_dir = Path(self.config.get('output_dir', 'reports'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.template_dir = Path(self.config.get('template_dir', 'templates'))
        self.include_raw_data = self.config.get('include_raw_data', False)
        self.generate_charts = self.config.get('generate_charts', True)
        
        # 报告模板
        self.report_templates = self._load_report_templates()
    
    async def generate_comprehensive_report(
        self,
        analysis_results: Dict[str, Any],
        api_list: List[APIInfo],
        security_results: List[SecurityCheckResult],
        format_type: str = 'html',
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成综合安全报告
        
        Args:
            analysis_results: 分析结果
            api_list: API列表
            security_results: 安全检测结果
            format_type: 报告格式 (html, json, pdf, markdown)
            custom_config: 自定义配置
            
        Returns:
            报告生成结果
        """
        try:
            logger.info(f"开始生成{format_type}格式的综合安全报告")
            
            # 合并配置
            report_config = self.config.copy()
            if custom_config:
                report_config.update(custom_config)
            
            # 生成报告数据
            report_data = await self._prepare_report_data(
                analysis_results, api_list, security_results, report_config
            )
            
            # 使用LLM生成报告内容
            llm_content = await self._generate_llm_content(report_data, report_config)
            report_data['llm_generated_content'] = llm_content
            
            # 根据格式生成报告
            if format_type == 'html':
                report_result = await self._generate_html_report(report_data, report_config)
            elif format_type == 'json':
                report_result = await self._generate_json_report(report_data, report_config)
            elif format_type == 'markdown':
                report_result = await self._generate_markdown_report(report_data, report_config)
            elif format_type == 'pdf':
                report_result = await self._generate_pdf_report(report_data, report_config)
            else:
                raise ValueError(f"不支持的报告格式: {format_type}")
            
            logger.info(f"报告生成完成: {report_result.get('file_path')}")
            
            return {
                'success': True,
                'report_result': report_result,
                'generation_time': datetime.now().isoformat(),
                'report_format': format_type
            }
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'generation_time': datetime.now().isoformat()
            }
    
    async def _prepare_report_data(
        self,
        analysis_results: Dict[str, Any],
        api_list: List[APIInfo],
        security_results: List[SecurityCheckResult],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """准备报告数据"""
        # 基础统计
        stats = self._calculate_statistics(api_list, security_results)
        
        # 风险分析
        risk_analysis = self._analyze_risk_distribution(security_results)
        
        # API分析
        api_analysis = self._analyze_api_patterns(api_list, security_results)
        
        # 时间线分析
        timeline_analysis = self._analyze_timeline(security_results)
        
        report_data = {
            'metadata': {
                'report_title': config.get('report_title', '淘宝API安全评估报告'),
                'generation_time': datetime.now().isoformat(),
                'report_version': '1.0',
                'analyzer_version': '1.0.0'
            },
            'executive_summary': {
                'total_apis_tested': len(api_list),
                'total_vulnerabilities_found': len(security_results),
                'critical_issues': len([r for r in security_results if r.risk_level == 'critical']),
                'high_risk_issues': len([r for r in security_results if r.risk_level == 'high']),
                'medium_risk_issues': len([r for r in security_results if r.risk_level == 'medium']),
                'low_risk_issues': len([r for r in security_results if r.risk_level == 'low'])
            },
            'detailed_statistics': stats,
            'risk_analysis': risk_analysis,
            'api_analysis': api_analysis,
            'timeline_analysis': timeline_analysis,
            'intelligent_analysis': analysis_results,
            'security_findings': [
                {
                    'title': result.title,
                    'description': result.description,
                    'risk_level': result.risk_level,
                    'vulnerability_type': result.check_type,
                    'api_url': result.api_info.url,
                    'evidence': result.evidence,
                    'recommendation': result.recommendation,
                    'technical_details': result.technical_details
                }
                for result in security_results
            ]
        }
        
        # 可选包含原始数据
        if self.include_raw_data:
            report_data['raw_data'] = {
                'api_list': [api.dict() for api in api_list],
                'security_results': [result.dict() for result in security_results]
            }
        
        return report_data
    
    async def _generate_llm_content(
        self,
        report_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """使用LLM生成报告内容"""
        try:
            llm_content = {}
            
            # 生成执行摘要
            executive_summary = await self._generate_executive_summary(report_data)
            llm_content['executive_summary'] = executive_summary
            
            # 生成关键发现
            key_findings = await self._generate_key_findings(report_data)
            llm_content['key_findings'] = key_findings
            
            # 生成修复建议
            remediation_plan = await self._generate_remediation_plan(report_data)
            llm_content['remediation_plan'] = remediation_plan
            
            # 生成结论和建议
            conclusions = await self._generate_conclusions(report_data)
            llm_content['conclusions'] = conclusions
            
            return llm_content
            
        except Exception as e:
            logger.error(f"LLM内容生成失败: {e}")
            return {}
    
    async def _generate_executive_summary(self, report_data: Dict[str, Any]) -> str:
        """生成执行摘要"""
        system_prompt = """你是一个专业的网络安全顾问。请基于API安全评估结果，为高级管理层撰写简洁明了的执行摘要。

摘要要求：
1. 突出关键安全风险
2. 量化业务影响
3. 提供明确的行动建议
4. 语言简洁专业，适合管理层阅读
5. 长度控制在300字以内"""
        
        user_prompt = f"""请为以下API安全评估结果撰写执行摘要：

评估范围：
- 测试API数量: {report_data['executive_summary']['total_apis_tested']}
- 发现漏洞总数: {report_data['executive_summary']['total_vulnerabilities_found']}
- 严重风险: {report_data['executive_summary']['critical_issues']}个
- 高风险: {report_data['executive_summary']['high_risk_issues']}个
- 中风险: {report_data['executive_summary']['medium_risk_issues']}个
- 低风险: {report_data['executive_summary']['low_risk_issues']}个

主要发现：
{json.dumps(report_data.get('intelligent_analysis', {}), indent=2, ensure_ascii=False)}

请撰写专业的执行摘要。"""
        
        try:
            response = await self.llm_provider.generate_response(
                messages=[{"role": "user", "content": user_prompt}],
                system_prompt=system_prompt,
                temperature=0.1
            )
            return response.content
        except Exception as e:
            logger.error(f"生成执行摘要失败: {e}")
            return "执行摘要生成失败"
    
    async def _generate_key_findings(self, report_data: Dict[str, Any]) -> str:
        """生成关键发现"""
        system_prompt = """你是一个API安全专家。请基于安全评估结果，总结最重要的安全发现。

要求：
1. 按风险等级排序
2. 突出最关键的安全问题
3. 说明潜在影响
4. 提供具体的技术细节
5. 使用专业但易懂的语言"""
        
        # 提取高风险问题
        high_risk_findings = [
            finding for finding in report_data.get('security_findings', [])
            if finding['risk_level'] in ['critical', 'high']
        ]
        
        user_prompt = f"""请总结以下安全评估的关键发现：

高风险安全问题：
{json.dumps(high_risk_findings[:10], indent=2, ensure_ascii=False)}

智能分析结果：
{json.dumps(report_data.get('intelligent_analysis', {}), indent=2, ensure_ascii=False)}

请提供详细的关键发现总结。"""
        
        try:
            response = await self.llm_provider.generate_response(
                messages=[{"role": "user", "content": user_prompt}],
                system_prompt=system_prompt,
                temperature=0.1
            )
            return response.content
        except Exception as e:
            logger.error(f"生成关键发现失败: {e}")
            return "关键发现生成失败"
    
    async def _generate_remediation_plan(self, report_data: Dict[str, Any]) -> str:
        """生成修复计划"""
        system_prompt = """你是一个安全修复专家。请基于发现的安全问题，制定详细的修复计划。

计划要求：
1. 按优先级排序修复任务
2. 提供具体的修复步骤
3. 估算修复时间和资源
4. 考虑业务影响和实施难度
5. 包含验证和测试建议"""
        
        user_prompt = f"""请制定以下安全问题的修复计划：

安全问题列表：
{json.dumps(report_data.get('security_findings', [])[:15], indent=2, ensure_ascii=False)}

智能修复建议：
{json.dumps(report_data.get('intelligent_analysis', {}), indent=2, ensure_ascii=False)}

请提供详细的修复计划和实施路线图。"""
        
        try:
            response = await self.llm_provider.generate_response(
                messages=[{"role": "user", "content": user_prompt}],
                system_prompt=system_prompt,
                temperature=0.1
            )
            return response.content
        except Exception as e:
            logger.error(f"生成修复计划失败: {e}")
            return "修复计划生成失败"
    
    async def _generate_conclusions(self, report_data: Dict[str, Any]) -> str:
        """生成结论和建议"""
        system_prompt = """你是一个高级安全顾问。请基于完整的安全评估结果，提供专业的结论和长期建议。

要求：
1. 总体安全态势评估
2. 长期安全改进建议
3. 安全治理建议
4. 合规性考虑
5. 持续监控建议"""
        
        user_prompt = f"""请基于以下完整的安全评估结果提供结论和建议：

评估统计：
{json.dumps(report_data.get('executive_summary', {}), indent=2, ensure_ascii=False)}

风险分析：
{json.dumps(report_data.get('risk_analysis', {}), indent=2, ensure_ascii=False)}

智能分析：
{json.dumps(report_data.get('intelligent_analysis', {}), indent=2, ensure_ascii=False)}

请提供专业的结论和长期安全建议。"""
        
        try:
            response = await self.llm_provider.generate_response(
                messages=[{"role": "user", "content": user_prompt}],
                system_prompt=system_prompt,
                temperature=0.1
            )
            return response.content
        except Exception as e:
            logger.error(f"生成结论失败: {e}")
            return "结论生成失败"
    
    async def _generate_html_report(
        self,
        report_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成HTML报告"""
        try:
            from .html_report import HTMLReportGenerator
            
            html_generator = HTMLReportGenerator(config)
            return await html_generator.generate(report_data)
            
        except ImportError:
            logger.error("HTML报告生成器不可用")
            return {'error': 'HTML报告生成器不可用'}
    
    async def _generate_json_report(
        self,
        report_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成JSON报告"""
        try:
            from .json_report import JSONReportGenerator
            
            json_generator = JSONReportGenerator(config)
            return await json_generator.generate(report_data)
            
        except ImportError:
            logger.error("JSON报告生成器不可用")
            return {'error': 'JSON报告生成器不可用'}
    
    async def _generate_markdown_report(
        self,
        report_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成Markdown报告"""
        # 简化的Markdown生成
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"security_report_{timestamp}.md"
        file_path = self.output_dir / filename
        
        markdown_content = self._build_markdown_content(report_data)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        return {
            'file_path': str(file_path),
            'filename': filename,
            'format': 'markdown',
            'size': len(markdown_content)
        }
    
    async def _generate_pdf_report(
        self,
        report_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成PDF报告"""
        # 注意：PDF生成需要额外的依赖库
        logger.warning("PDF报告生成功能需要额外配置")
        return {'error': 'PDF报告生成功能暂未实现'}
    
    def _build_markdown_content(self, report_data: Dict[str, Any]) -> str:
        """构建Markdown内容"""
        content = f"""# {report_data['metadata']['report_title']}

**生成时间**: {report_data['metadata']['generation_time']}
**报告版本**: {report_data['metadata']['report_version']}

## 执行摘要

{report_data.get('llm_generated_content', {}).get('executive_summary', '暂无执行摘要')}

### 统计概览

- **测试API数量**: {report_data['executive_summary']['total_apis_tested']}
- **发现漏洞总数**: {report_data['executive_summary']['total_vulnerabilities_found']}
- **严重风险**: {report_data['executive_summary']['critical_issues']}个
- **高风险**: {report_data['executive_summary']['high_risk_issues']}个
- **中风险**: {report_data['executive_summary']['medium_risk_issues']}个
- **低风险**: {report_data['executive_summary']['low_risk_issues']}个

## 关键发现

{report_data.get('llm_generated_content', {}).get('key_findings', '暂无关键发现')}

## 修复计划

{report_data.get('llm_generated_content', {}).get('remediation_plan', '暂无修复计划')}

## 详细安全问题

"""
        
        # 添加安全问题详情
        for i, finding in enumerate(report_data.get('security_findings', [])[:20], 1):
            content += f"""### {i}. {finding['title']}

- **风险等级**: {finding['risk_level']}
- **漏洞类型**: {finding['vulnerability_type']}
- **影响API**: {finding['api_url']}
- **描述**: {finding['description']}
- **修复建议**: {finding['recommendation']}

"""
        
        content += f"""## 结论和建议

{report_data.get('llm_generated_content', {}).get('conclusions', '暂无结论')}

---
*本报告由淘宝API安全检测Agent自动生成*
"""
        
        return content
    
    def _calculate_statistics(
        self,
        api_list: List[APIInfo],
        security_results: List[SecurityCheckResult]
    ) -> Dict[str, Any]:
        """计算详细统计信息"""
        # API统计
        api_by_domain = {}
        api_by_method = {}
        
        for api in api_list:
            domain = api.domain
            method = api.method
            
            api_by_domain[domain] = api_by_domain.get(domain, 0) + 1
            api_by_method[method] = api_by_method.get(method, 0) + 1
        
        # 漏洞统计
        vuln_by_type = {}
        vuln_by_risk = {}
        
        for result in security_results:
            vuln_type = result.check_type
            risk_level = result.risk_level
            
            vuln_by_type[vuln_type] = vuln_by_type.get(vuln_type, 0) + 1
            vuln_by_risk[risk_level] = vuln_by_risk.get(risk_level, 0) + 1
        
        return {
            'api_statistics': {
                'by_domain': api_by_domain,
                'by_method': api_by_method,
                'total_domains': len(api_by_domain),
                'total_methods': len(api_by_method)
            },
            'vulnerability_statistics': {
                'by_type': vuln_by_type,
                'by_risk_level': vuln_by_risk,
                'total_types': len(vuln_by_type),
                'total_risk_levels': len(vuln_by_risk)
            }
        }
    
    def _analyze_risk_distribution(self, security_results: List[SecurityCheckResult]) -> Dict[str, Any]:
        """分析风险分布"""
        risk_levels = ['critical', 'high', 'medium', 'low', 'info']
        risk_counts = {level: 0 for level in risk_levels}
        
        for result in security_results:
            if result.risk_level in risk_counts:
                risk_counts[result.risk_level] += 1
        
        total = len(security_results)
        risk_percentages = {
            level: (count / total * 100) if total > 0 else 0
            for level, count in risk_counts.items()
        }
        
        return {
            'risk_counts': risk_counts,
            'risk_percentages': risk_percentages,
            'total_issues': total
        }
    
    def _analyze_api_patterns(
        self,
        api_list: List[APIInfo],
        security_results: List[SecurityCheckResult]
    ) -> Dict[str, Any]:
        """分析API模式"""
        # API安全问题分布
        api_issues = {}
        for result in security_results:
            api_url = result.api_info.url
            if api_url not in api_issues:
                api_issues[api_url] = []
            api_issues[api_url].append(result)
        
        # 最有问题的API
        most_vulnerable_apis = sorted(
            api_issues.items(),
            key=lambda x: len(x[1]),
            reverse=True
        )[:10]
        
        return {
            'total_apis_with_issues': len(api_issues),
            'most_vulnerable_apis': [
                {
                    'url': url,
                    'issue_count': len(issues),
                    'risk_levels': [issue.risk_level for issue in issues]
                }
                for url, issues in most_vulnerable_apis
            ]
        }
    
    def _analyze_timeline(self, security_results: List[SecurityCheckResult]) -> Dict[str, Any]:
        """分析时间线"""
        # 简化的时间线分析
        return {
            'analysis_start_time': min(
                result.api_info.timestamp for result in security_results
            ).isoformat() if security_results else None,
            'analysis_end_time': max(
                result.api_info.timestamp for result in security_results
            ).isoformat() if security_results else None,
            'total_analysis_duration': 'N/A'  # 需要更详细的时间跟踪
        }
    
    def _load_report_templates(self) -> Dict[str, str]:
        """加载报告模板"""
        # 这里可以从文件系统加载模板
        return {
            'html': 'default_html_template',
            'markdown': 'default_markdown_template',
            'json': 'default_json_template'
        }
