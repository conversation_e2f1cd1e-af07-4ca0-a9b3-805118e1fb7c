"""
HTML报告生成器

生成美观的HTML格式安全报告。
"""

import json
from typing import Dict, Any
from datetime import datetime
from pathlib import Path
import logging

from ..utils.logger import get_logger

logger = get_logger(__name__)


class HTMLReportGenerator:
    """HTML报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化HTML报告生成器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.output_dir = Path(config.get('output_dir', 'reports'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.include_charts = config.get('include_charts', True)
        self.theme = config.get('theme', 'default')
        self.include_raw_data = config.get('include_raw_data', False)
    
    async def generate(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成HTML报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            生成结果
        """
        try:
            logger.info("开始生成HTML报告")
            
            # 生成HTML内容
            html_content = self._build_html_content(report_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"security_report_{timestamp}.html"
            file_path = self.output_dir / filename
            
            # 写入HTML文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            file_size = file_path.stat().st_size
            
            logger.info(f"HTML报告生成完成: {file_path}")
            
            return {
                'file_path': str(file_path),
                'filename': filename,
                'format': 'html',
                'size': file_size,
                'theme': self.theme
            }
            
        except Exception as e:
            logger.error(f"生成HTML报告失败: {e}")
            raise
    
    def _build_html_content(self, report_data: Dict[str, Any]) -> str:
        """构建HTML内容"""
        html_template = self._get_html_template()
        
        # 替换模板变量
        html_content = html_template.format(
            report_title=report_data['metadata']['report_title'],
            generation_time=report_data['metadata']['generation_time'],
            executive_summary=self._build_executive_summary_html(report_data),
            statistics_section=self._build_statistics_html(report_data),
            findings_section=self._build_findings_html(report_data),
            charts_section=self._build_charts_html(report_data) if self.include_charts else '',
            llm_analysis_section=self._build_llm_analysis_html(report_data),
            raw_data_section=self._build_raw_data_html(report_data) if self.include_raw_data else '',
            css_styles=self._get_css_styles(),
            javascript_code=self._get_javascript_code()
        )
        
        return html_content
    
    def _build_executive_summary_html(self, report_data: Dict[str, Any]) -> str:
        """构建执行摘要HTML"""
        summary = report_data['executive_summary']
        llm_summary = report_data.get('llm_generated_content', {}).get('executive_summary', '')
        
        return f"""
        <div class="executive-summary">
            <h2>执行摘要</h2>
            <div class="summary-stats">
                <div class="stat-card">
                    <h3>{summary['total_apis_tested']}</h3>
                    <p>测试API数量</p>
                </div>
                <div class="stat-card">
                    <h3>{summary['total_vulnerabilities_found']}</h3>
                    <p>发现漏洞总数</p>
                </div>
                <div class="stat-card critical">
                    <h3>{summary['critical_issues']}</h3>
                    <p>严重风险</p>
                </div>
                <div class="stat-card high">
                    <h3>{summary['high_risk_issues']}</h3>
                    <p>高风险</p>
                </div>
                <div class="stat-card medium">
                    <h3>{summary['medium_risk_issues']}</h3>
                    <p>中风险</p>
                </div>
                <div class="stat-card low">
                    <h3>{summary['low_risk_issues']}</h3>
                    <p>低风险</p>
                </div>
            </div>
            <div class="llm-summary">
                <h3>智能分析摘要</h3>
                <div class="summary-content">
                    {llm_summary.replace('\n', '<br>')}
                </div>
            </div>
        </div>
        """
    
    def _build_statistics_html(self, report_data: Dict[str, Any]) -> str:
        """构建统计信息HTML"""
        stats = report_data.get('detailed_statistics', {})
        
        return f"""
        <div class="statistics-section">
            <h2>详细统计</h2>
            <div class="stats-grid">
                <div class="stats-card">
                    <h3>API统计</h3>
                    <ul>
                        <li>总域名数: {stats.get('api_statistics', {}).get('total_domains', 0)}</li>
                        <li>HTTP方法数: {stats.get('api_statistics', {}).get('total_methods', 0)}</li>
                    </ul>
                </div>
                <div class="stats-card">
                    <h3>漏洞统计</h3>
                    <ul>
                        <li>漏洞类型数: {stats.get('vulnerability_statistics', {}).get('total_types', 0)}</li>
                        <li>风险等级数: {stats.get('vulnerability_statistics', {}).get('total_risk_levels', 0)}</li>
                    </ul>
                </div>
            </div>
        </div>
        """
    
    def _build_findings_html(self, report_data: Dict[str, Any]) -> str:
        """构建安全发现HTML"""
        findings = report_data.get('security_findings', [])
        
        findings_html = """
        <div class="findings-section">
            <h2>安全发现</h2>
            <div class="findings-list">
        """
        
        for i, finding in enumerate(findings[:20], 1):  # 限制显示数量
            risk_class = finding['risk_level'].lower()
            findings_html += f"""
            <div class="finding-card {risk_class}">
                <div class="finding-header">
                    <h3>{i}. {finding['title']}</h3>
                    <span class="risk-badge {risk_class}">{finding['risk_level'].upper()}</span>
                </div>
                <div class="finding-content">
                    <p><strong>API:</strong> {finding['api_url']}</p>
                    <p><strong>类型:</strong> {finding['vulnerability_type']}</p>
                    <p><strong>描述:</strong> {finding['description']}</p>
                    <p><strong>修复建议:</strong> {finding['recommendation']}</p>
                    {self._build_evidence_html(finding.get('evidence', []))}
                </div>
            </div>
            """
        
        findings_html += """
            </div>
        </div>
        """
        
        return findings_html
    
    def _build_evidence_html(self, evidence: list) -> str:
        """构建证据HTML"""
        if not evidence:
            return ""
        
        evidence_html = "<div class='evidence'><strong>证据:</strong><ul>"
        for item in evidence[:5]:  # 限制证据数量
            evidence_html += f"<li>{item}</li>"
        evidence_html += "</ul></div>"
        
        return evidence_html
    
    def _build_charts_html(self, report_data: Dict[str, Any]) -> str:
        """构建图表HTML"""
        if not self.include_charts:
            return ""
        
        risk_analysis = report_data.get('risk_analysis', {})
        
        return f"""
        <div class="charts-section">
            <h2>可视化分析</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>风险分布</h3>
                    <canvas id="riskChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>漏洞类型分布</h3>
                    <canvas id="vulnTypeChart"></canvas>
                </div>
            </div>
            <script>
                // 风险分布图表数据
                const riskData = {json.dumps(risk_analysis.get('risk_counts', {}))};
                // 这里可以添加Chart.js代码来渲染图表
            </script>
        </div>
        """
    
    def _build_llm_analysis_html(self, report_data: Dict[str, Any]) -> str:
        """构建LLM分析HTML"""
        llm_content = report_data.get('llm_generated_content', {})
        
        return f"""
        <div class="llm-analysis-section">
            <h2>智能分析</h2>
            
            <div class="analysis-card">
                <h3>关键发现</h3>
                <div class="analysis-content">
                    {llm_content.get('key_findings', '暂无关键发现').replace('\n', '<br>')}
                </div>
            </div>
            
            <div class="analysis-card">
                <h3>修复计划</h3>
                <div class="analysis-content">
                    {llm_content.get('remediation_plan', '暂无修复计划').replace('\n', '<br>')}
                </div>
            </div>
            
            <div class="analysis-card">
                <h3>结论和建议</h3>
                <div class="analysis-content">
                    {llm_content.get('conclusions', '暂无结论').replace('\n', '<br>')}
                </div>
            </div>
        </div>
        """
    
    def _build_raw_data_html(self, report_data: Dict[str, Any]) -> str:
        """构建原始数据HTML"""
        if not self.include_raw_data:
            return ""
        
        raw_data = report_data.get('raw_data', {})
        
        return f"""
        <div class="raw-data-section">
            <h2>原始数据</h2>
            <details>
                <summary>点击查看原始数据</summary>
                <pre><code>{json.dumps(raw_data, indent=2, ensure_ascii=False)}</code></pre>
            </details>
        </div>
        """
    
    def _get_html_template(self) -> str:
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report_title}</title>
    <style>{css_styles}</style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{report_title}</h1>
            <p class="generation-time">生成时间: {generation_time}</p>
        </header>
        
        <main class="report-content">
            {executive_summary}
            {statistics_section}
            {findings_section}
            {charts_section}
            {llm_analysis_section}
            {raw_data_section}
        </main>
        
        <footer class="report-footer">
            <p>本报告由淘宝API安全检测Agent自动生成</p>
        </footer>
    </div>
    
    <script>{javascript_code}</script>
</body>
</html>
        """
    
    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .report-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .generation-time {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .executive-summary {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .stat-card.critical {
            border-left-color: #dc3545;
        }
        
        .stat-card.high {
            border-left-color: #fd7e14;
        }
        
        .stat-card.medium {
            border-left-color: #ffc107;
        }
        
        .stat-card.low {
            border-left-color: #28a745;
        }
        
        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
            color: #333;
        }
        
        .finding-card {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        
        .finding-card.critical {
            border-left-color: #dc3545;
        }
        
        .finding-card.high {
            border-left-color: #fd7e14;
        }
        
        .finding-card.medium {
            border-left-color: #ffc107;
        }
        
        .finding-card.low {
            border-left-color: #28a745;
        }
        
        .finding-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .finding-content {
            padding: 20px;
        }
        
        .risk-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
        }
        
        .risk-badge.critical {
            background-color: #dc3545;
        }
        
        .risk-badge.high {
            background-color: #fd7e14;
        }
        
        .risk-badge.medium {
            background-color: #ffc107;
            color: #333;
        }
        
        .risk-badge.low {
            background-color: #28a745;
        }
        
        .analysis-card {
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .analysis-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .analysis-content {
            line-height: 1.8;
        }
        
        .report-footer {
            text-align: center;
            padding: 20px;
            color: #666;
            margin-top: 40px;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9em;
        }
        
        details {
            margin-top: 20px;
        }
        
        summary {
            cursor: pointer;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .finding-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        """
    
    def _get_javascript_code(self) -> str:
        """获取JavaScript代码"""
        return """
        // 可以添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 添加平滑滚动
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
            
            // 添加展开/折叠功能
            const findingCards = document.querySelectorAll('.finding-card');
            findingCards.forEach(card => {
                const header = card.querySelector('.finding-header');
                const content = card.querySelector('.finding-content');
                
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    content.style.display = content.style.display === 'none' ? 'block' : 'none';
                });
            });
        });
        """
