"""
JSON报告生成器

生成结构化的JSON格式安全报告。
"""

import json
from typing import Dict, Any
from datetime import datetime
from pathlib import Path
import logging

from ..utils.logger import get_logger

logger = get_logger(__name__)


class JSONReportGenerator:
    """JSON报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化JSON报告生成器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.output_dir = Path(config.get('output_dir', 'reports'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.pretty_print = config.get('pretty_print', True)
        self.include_metadata = config.get('include_metadata', True)
        self.compress_output = config.get('compress_output', False)
    
    async def generate(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成JSON报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            生成结果
        """
        try:
            logger.info("开始生成JSON报告")
            
            # 准备JSON数据
            json_data = self._prepare_json_data(report_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"security_report_{timestamp}.json"
            file_path = self.output_dir / filename
            
            # 写入JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                if self.pretty_print:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                else:
                    json.dump(json_data, f, ensure_ascii=False)
            
            # 可选压缩
            if self.compress_output:
                compressed_path = await self._compress_file(file_path)
                file_path = compressed_path
                filename = compressed_path.name
            
            file_size = file_path.stat().st_size
            
            logger.info(f"JSON报告生成完成: {file_path}")
            
            return {
                'file_path': str(file_path),
                'filename': filename,
                'format': 'json',
                'size': file_size,
                'compressed': self.compress_output
            }
            
        except Exception as e:
            logger.error(f"生成JSON报告失败: {e}")
            raise
    
    def _prepare_json_data(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备JSON数据"""
        json_data = {
            'report_info': {
                'format': 'json',
                'version': '1.0',
                'generator': 'TaobaoAPISecurityAgent',
                'generation_timestamp': datetime.now().isoformat()
            }
        }
        
        # 添加报告数据
        json_data.update(report_data)
        
        # 添加元数据
        if self.include_metadata:
            json_data['metadata'].update({
                'json_generator_config': {
                    'pretty_print': self.pretty_print,
                    'compressed': self.compress_output
                }
            })
        
        # 数据清理和序列化
        json_data = self._clean_data_for_json(json_data)
        
        return json_data
    
    def _clean_data_for_json(self, data: Any) -> Any:
        """清理数据以确保JSON序列化兼容"""
        if isinstance(data, dict):
            return {key: self._clean_data_for_json(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._clean_data_for_json(item) for item in data]
        elif isinstance(data, datetime):
            return data.isoformat()
        elif hasattr(data, 'dict'):  # Pydantic模型
            return self._clean_data_for_json(data.dict())
        elif hasattr(data, '__dict__'):  # 其他对象
            return self._clean_data_for_json(data.__dict__)
        else:
            # 基本类型或已经是JSON兼容的
            return data
    
    async def _compress_file(self, file_path: Path) -> Path:
        """压缩文件"""
        try:
            import gzip
            
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # 删除原文件
            file_path.unlink()
            
            return compressed_path
            
        except ImportError:
            logger.warning("gzip模块不可用，跳过压缩")
            return file_path
        except Exception as e:
            logger.error(f"文件压缩失败: {e}")
            return file_path
