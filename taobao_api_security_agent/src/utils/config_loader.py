"""
配置加载器模块

负责加载和管理各种配置文件。
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
import logging

logger = logging.getLogger(__name__)


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Dict[str, Any]] = {}
        
        # 确保配置目录存在
        if not self.config_dir.exists():
            raise FileNotFoundError(f"配置目录不存在: {self.config_dir}")
    
    def load_config(self, config_name: str, reload: bool = False) -> Dict[str, Any]:
        """
        加载指定的配置文件
        
        Args:
            config_name: 配置文件名(不含扩展名)
            reload: 是否强制重新加载
            
        Returns:
            配置字典
        """
        if config_name in self._configs and not reload:
            return self._configs[config_name]
        
        config_file = self.config_dir / f"{config_name}.yaml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 处理环境变量替换
            config_data = self._resolve_env_vars(config_data)
            
            self._configs[config_name] = config_data
            logger.info(f"成功加载配置文件: {config_file}")
            
            return config_data
            
        except yaml.YAMLError as e:
            logger.error(f"解析YAML配置文件失败: {config_file}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_file}, 错误: {e}")
            raise
    
    def load_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        加载所有配置文件
        
        Returns:
            所有配置的字典
        """
        config_files = [
            "agent_config",
            "llm_config", 
            "signature_config",
            "session_config"
        ]
        
        all_configs = {}
        for config_name in config_files:
            try:
                all_configs[config_name] = self.load_config(config_name)
            except FileNotFoundError:
                logger.warning(f"配置文件不存在，跳过: {config_name}.yaml")
            except Exception as e:
                logger.error(f"加载配置文件失败: {config_name}.yaml, 错误: {e}")
                raise
        
        return all_configs
    
    def get_config(self, config_name: str, key_path: str = None) -> Any:
        """
        获取配置值
        
        Args:
            config_name: 配置文件名
            key_path: 配置键路径，如 "security_checks.field_exposure.enabled"
            
        Returns:
            配置值
        """
        config = self.load_config(config_name)
        
        if key_path is None:
            return config
        
        keys = key_path.split('.')
        value = config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            logger.warning(f"配置键不存在: {config_name}.{key_path}")
            return None
    
    def _resolve_env_vars(self, data: Any) -> Any:
        """
        递归解析环境变量
        
        Args:
            data: 配置数据
            
        Returns:
            解析后的数据
        """
        if isinstance(data, dict):
            return {key: self._resolve_env_vars(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._resolve_env_vars(item) for item in data]
        elif isinstance(data, str) and data.startswith('${') and data.endswith('}'):
            # 解析环境变量 ${VAR_NAME} 或 ${VAR_NAME:default_value}
            env_var = data[2:-1]  # 移除 ${ 和 }
            
            if ':' in env_var:
                var_name, default_value = env_var.split(':', 1)
                return os.getenv(var_name, default_value)
            else:
                env_value = os.getenv(env_var)
                if env_value is None:
                    logger.warning(f"环境变量未设置: {env_var}")
                return env_value
        else:
            return data
    
    def validate_config(self, config_name: str) -> bool:
        """
        验证配置文件的有效性
        
        Args:
            config_name: 配置文件名
            
        Returns:
            是否有效
        """
        try:
            config = self.load_config(config_name)
            
            # 基本验证
            if not isinstance(config, dict):
                logger.error(f"配置文件格式错误: {config_name}")
                return False
            
            # 特定配置的验证
            if config_name == "agent_config":
                return self._validate_agent_config(config)
            elif config_name == "llm_config":
                return self._validate_llm_config(config)
            elif config_name == "signature_config":
                return self._validate_signature_config(config)
            elif config_name == "session_config":
                return self._validate_session_config(config)
            
            return True
            
        except Exception as e:
            logger.error(f"验证配置文件失败: {config_name}, 错误: {e}")
            return False
    
    def _validate_agent_config(self, config: Dict[str, Any]) -> bool:
        """验证Agent配置"""
        required_keys = ["agent", "targets", "security_checks"]
        
        for key in required_keys:
            if key not in config:
                logger.error(f"Agent配置缺少必需键: {key}")
                return False
        
        return True
    
    def _validate_llm_config(self, config: Dict[str, Any]) -> bool:
        """验证LLM配置"""
        required_keys = ["default_provider", "providers"]
        
        for key in required_keys:
            if key not in config:
                logger.error(f"LLM配置缺少必需键: {key}")
                return False
        
        # 验证默认提供商是否在providers中
        default_provider = config.get("default_provider")
        providers = config.get("providers", {})
        
        if default_provider not in providers:
            logger.error(f"默认LLM提供商不存在: {default_provider}")
            return False
        
        return True
    
    def _validate_signature_config(self, config: Dict[str, Any]) -> bool:
        """验证签名配置"""
        required_keys = ["signature_service", "signature_algorithms"]
        
        for key in required_keys:
            if key not in config:
                logger.error(f"签名配置缺少必需键: {key}")
                return False
        
        return True
    
    def _validate_session_config(self, config: Dict[str, Any]) -> bool:
        """验证会话配置"""
        required_keys = ["session_manager", "user_sessions"]
        
        for key in required_keys:
            if key not in config:
                logger.error(f"会话配置缺少必需键: {key}")
                return False
        
        return True


class EnvironmentConfig(BaseSettings):
    """环境配置模型"""
    
    # API密钥
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    dashscope_api_key: Optional[str] = Field(None, env="DASHSCOPE_API_KEY")
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    
    # 签名服务
    taobao_signature_service_url: Optional[str] = Field(None, env="TAOBAO_SIGNATURE_SERVICE_URL")
    taobao_signature_service_fallback_url: Optional[str] = Field(None, env="TAOBAO_SIGNATURE_SERVICE_FALLBACK_URL")
    signature_service_api_key: Optional[str] = Field(None, env="SIGNATURE_SERVICE_API_KEY")
    
    # 淘宝密钥
    taobao_secret_key: Optional[str] = Field(None, env="TAOBAO_SECRET_KEY")
    taobao_md5_key: Optional[str] = Field(None, env="TAOBAO_MD5_KEY")
    taobao_app_key: Optional[str] = Field(None, env="TAOBAO_APP_KEY")
    taobao_app_secret: Optional[str] = Field(None, env="TAOBAO_APP_SECRET")
    
    # 数据库和缓存
    database_url: Optional[str] = Field(None, env="DATABASE_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    
    # 加密密钥
    cookie_encryption_key: Optional[str] = Field(None, env="COOKIE_ENCRYPTION_KEY")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置加载器实例
_config_loader: Optional[ConfigLoader] = None


def get_config_loader(config_dir: str = "config") -> ConfigLoader:
    """
    获取全局配置加载器实例
    
    Args:
        config_dir: 配置目录
        
    Returns:
        配置加载器实例
    """
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ConfigLoader(config_dir)
    
    return _config_loader


def load_env_config() -> EnvironmentConfig:
    """
    加载环境配置
    
    Returns:
        环境配置实例
    """
    return EnvironmentConfig()
