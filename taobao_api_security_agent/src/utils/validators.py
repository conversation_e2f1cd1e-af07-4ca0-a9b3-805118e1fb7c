"""
数据验证器模块

提供各种数据验证功能。
"""

import re
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse
from ..agent.state import APIInfo, SecurityCheckResult


def validate_url(url: str) -> bool:
    """
    验证URL格式是否正确
    
    Args:
        url: 待验证的URL
        
    Returns:
        是否有效
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_taobao_domain(url: str) -> bool:
    """
    验证是否为淘宝相关域名
    
    Args:
        url: 待验证的URL
        
    Returns:
        是否为淘宝域名
    """
    if not validate_url(url):
        return False
    
    parsed = urlparse(url)
    domain = parsed.netloc.lower()
    
    # 淘宝相关域名列表
    taobao_domains = [
        'taobao.com',
        'tmall.com',
        'alibaba.com',
        'alipay.com',
        'alicdn.com',
        'tbcdn.cn'
    ]
    
    for taobao_domain in taobao_domains:
        if domain.endswith(taobao_domain):
            return True
    
    return False


def validate_api_info(api_info: APIInfo) -> List[str]:
    """
    验证API信息的完整性
    
    Args:
        api_info: API信息对象
        
    Returns:
        验证错误列表，空列表表示验证通过
    """
    errors = []
    
    # 验证URL
    if not validate_url(api_info.url):
        errors.append("无效的URL格式")
    
    # 验证HTTP方法
    valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
    if api_info.method.upper() not in valid_methods:
        errors.append(f"无效的HTTP方法: {api_info.method}")
    
    # 验证域名
    if not api_info.domain:
        errors.append("域名不能为空")
    
    # 验证路径
    if not api_info.path:
        errors.append("路径不能为空")
    
    return errors


def validate_config(config: Dict[str, Any]) -> List[str]:
    """
    验证配置文件的有效性
    
    Args:
        config: 配置字典
        
    Returns:
        验证错误列表
    """
    errors = []
    
    # 基本结构验证
    if not isinstance(config, dict):
        errors.append("配置必须是字典格式")
        return errors
    
    return errors


def validate_security_result(result: SecurityCheckResult) -> List[str]:
    """
    验证安全检查结果
    
    Args:
        result: 安全检查结果
        
    Returns:
        验证错误列表
    """
    errors = []
    
    # 验证必需字段
    if not result.title:
        errors.append("标题不能为空")
    
    if not result.description:
        errors.append("描述不能为空")
    
    # 验证API信息
    api_errors = validate_api_info(result.api_info)
    if api_errors:
        errors.extend([f"API信息错误: {error}" for error in api_errors])
    
    return errors


def validate_http_headers(headers: Dict[str, str]) -> List[str]:
    """
    验证HTTP头部格式
    
    Args:
        headers: HTTP头部字典
        
    Returns:
        验证错误列表
    """
    errors = []
    
    # 头部名称验证正则
    header_name_pattern = re.compile(r'^[a-zA-Z0-9!#$&\-\^_`|~]+$')
    
    for name, value in headers.items():
        # 验证头部名称
        if not header_name_pattern.match(name):
            errors.append(f"无效的头部名称: {name}")
        
        # 验证头部值（不能包含换行符）
        if '\n' in value or '\r' in value:
            errors.append(f"头部值不能包含换行符: {name}")
    
    return errors


def validate_json_data(data: str) -> bool:
    """
    验证JSON数据格式
    
    Args:
        data: JSON字符串
        
    Returns:
        是否有效
    """
    try:
        import json
        json.loads(data)
        return True
    except (json.JSONDecodeError, TypeError):
        return False


def sanitize_input(input_str: str, max_length: int = 10000) -> str:
    """
    清理输入字符串
    
    Args:
        input_str: 输入字符串
        max_length: 最大长度
        
    Returns:
        清理后的字符串
    """
    if not isinstance(input_str, str):
        input_str = str(input_str)
    
    # 限制长度
    if len(input_str) > max_length:
        input_str = input_str[:max_length]
    
    # 移除控制字符（保留换行和制表符）
    cleaned = ''.join(char for char in input_str 
                     if ord(char) >= 32 or char in '\n\t\r')
    
    return cleaned.strip()


def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """
    验证文件扩展名
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名列表
        
    Returns:
        是否有效
    """
    if not filename or '.' not in filename:
        return False
    
    extension = filename.split('.')[-1].lower()
    return extension in [ext.lower() for ext in allowed_extensions]
