"""
API资产发现引擎

企业级API资产自动发现和管理系统。
"""

import asyncio
import re
import json
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime
from urllib.parse import urlparse, urljoin
import logging

from ..utils.logger import get_logger
from ..agent.state import APIInfo

logger = get_logger(__name__)


@dataclass
class APIAsset:
    """API资产模型"""
    url: str
    method: str
    domain: str
    path: str
    discovery_method: str
    discovery_time: datetime
    parameters: Dict[str, Any]
    response_schema: Optional[Dict[str, Any]] = None
    authentication_required: bool = False
    rate_limit_info: Optional[Dict[str, Any]] = None
    business_criticality: str = "medium"  # critical, high, medium, low
    tags: List[str] = None
    status: str = "active"  # active, inactive, deprecated
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class APIAssetDiscoveryEngine:
    """API资产发现引擎"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化API资产发现引擎
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 发现方法配置
        self.discovery_methods = self.config.get('discovery_methods', [
            'traffic_analysis',
            'web_crawling', 
            'subdomain_enumeration',
            'documentation_parsing',
            'port_scanning'
        ])
        
        # 发现配置
        self.max_depth = self.config.get('max_crawl_depth', 3)
        self.max_pages = self.config.get('max_pages_per_domain', 1000)
        self.concurrent_requests = self.config.get('concurrent_requests', 10)
        
        # 资产存储
        self.discovered_assets: Dict[str, APIAsset] = {}
        self.discovery_stats = {
            'total_discovered': 0,
            'by_method': {},
            'by_domain': {},
            'start_time': None,
            'end_time': None
        }
        
        # 过滤规则
        self.exclusion_patterns = self.config.get('exclusion_patterns', [
            r'.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|ttf)$',
            r'.*/static/.*',
            r'.*/assets/.*'
        ])
        
        # 业务关键词识别
        self.business_keywords = {
            'critical': ['admin', 'manage', 'control', 'system', 'config'],
            'high': ['user', 'account', 'profile', 'order', 'payment'],
            'medium': ['product', 'search', 'category', 'list'],
            'low': ['static', 'public', 'common', 'util']
        }
    
    async def discover_assets(self, target_domains: List[str]) -> List[APIAsset]:
        """
        发现API资产
        
        Args:
            target_domains: 目标域名列表
            
        Returns:
            发现的API资产列表
        """
        logger.info(f"开始API资产发现，目标域名: {target_domains}")
        self.discovery_stats['start_time'] = datetime.now()
        
        try:
            # 并行执行多种发现方法
            discovery_tasks = []
            
            for domain in target_domains:
                for method in self.discovery_methods:
                    task = self._execute_discovery_method(method, domain)
                    discovery_tasks.append(task)
            
            # 等待所有发现任务完成
            results = await asyncio.gather(*discovery_tasks, return_exceptions=True)
            
            # 处理发现结果
            for result in results:
                if isinstance(result, list):
                    for asset in result:
                        await self._add_discovered_asset(asset)
                elif isinstance(result, Exception):
                    logger.error(f"发现任务失败: {result}")
            
            # 后处理：去重、分类、评估
            await self._post_process_assets()
            
            self.discovery_stats['end_time'] = datetime.now()
            self.discovery_stats['total_discovered'] = len(self.discovered_assets)
            
            logger.info(f"API资产发现完成，共发现 {len(self.discovered_assets)} 个API")
            
            return list(self.discovered_assets.values())
            
        except Exception as e:
            logger.error(f"API资产发现失败: {e}")
            raise
    
    async def _execute_discovery_method(self, method: str, domain: str) -> List[APIAsset]:
        """执行特定的发现方法"""
        try:
            if method == 'traffic_analysis':
                return await self._discover_by_traffic_analysis(domain)
            elif method == 'web_crawling':
                return await self._discover_by_web_crawling(domain)
            elif method == 'subdomain_enumeration':
                return await self._discover_by_subdomain_enumeration(domain)
            elif method == 'documentation_parsing':
                return await self._discover_by_documentation_parsing(domain)
            elif method == 'port_scanning':
                return await self._discover_by_port_scanning(domain)
            else:
                logger.warning(f"未知的发现方法: {method}")
                return []
                
        except Exception as e:
            logger.error(f"发现方法 {method} 执行失败: {e}")
            return []
    
    async def _discover_by_traffic_analysis(self, domain: str) -> List[APIAsset]:
        """通过流量分析发现API"""
        logger.info(f"通过流量分析发现API: {domain}")
        
        # 这里应该集成流量采集代理的数据
        # 模拟实现
        discovered_apis = []
        
        # 模拟从流量日志中提取的API
        sample_traffic_apis = [
            {
                'url': f'https://{domain}/api/user/profile',
                'method': 'GET',
                'parameters': {'userId': 'string', 'fields': 'string'}
            },
            {
                'url': f'https://{domain}/api/product/search',
                'method': 'GET', 
                'parameters': {'q': 'string', 'category': 'string', 'page': 'int'}
            }
        ]
        
        for api_data in sample_traffic_apis:
            asset = self._create_api_asset(
                api_data['url'],
                api_data['method'],
                'traffic_analysis',
                api_data.get('parameters', {})
            )
            discovered_apis.append(asset)
        
        return discovered_apis
    
    async def _discover_by_web_crawling(self, domain: str) -> List[APIAsset]:
        """通过Web爬虫发现API"""
        logger.info(f"通过Web爬虫发现API: {domain}")
        
        discovered_apis = []
        visited_urls = set()
        
        # 起始URL
        start_urls = [
            f'https://{domain}',
            f'https://{domain}/api',
            f'https://{domain}/v1',
            f'https://{domain}/v2'
        ]
        
        for start_url in start_urls:
            try:
                apis = await self._crawl_for_apis(start_url, visited_urls, depth=0)
                discovered_apis.extend(apis)
            except Exception as e:
                logger.error(f"爬虫发现失败 {start_url}: {e}")
        
        return discovered_apis
    
    async def _crawl_for_apis(self, url: str, visited: Set[str], depth: int) -> List[APIAsset]:
        """递归爬虫发现API"""
        if depth > self.max_depth or url in visited or len(visited) > self.max_pages:
            return []
        
        visited.add(url)
        discovered_apis = []
        
        try:
            # 模拟HTTP请求
            # 在实际实现中，这里应该使用aiohttp等库
            
            # 检查是否是API端点
            if self._is_api_endpoint(url):
                # 尝试不同的HTTP方法
                for method in ['GET', 'POST', 'PUT', 'DELETE']:
                    asset = self._create_api_asset(url, method, 'web_crawling')
                    discovered_apis.append(asset)
            
            # 提取页面中的链接（模拟）
            links = self._extract_links_from_page(url)
            
            # 递归爬虫
            for link in links:
                if not self._should_exclude_url(link):
                    sub_apis = await self._crawl_for_apis(link, visited, depth + 1)
                    discovered_apis.extend(sub_apis)
        
        except Exception as e:
            logger.error(f"爬虫处理URL失败 {url}: {e}")
        
        return discovered_apis
    
    async def _discover_by_subdomain_enumeration(self, domain: str) -> List[APIAsset]:
        """通过子域名枚举发现API"""
        logger.info(f"通过子域名枚举发现API: {domain}")
        
        discovered_apis = []
        
        # 常见的API子域名
        api_subdomains = [
            'api', 'apis', 'gateway', 'service', 'services',
            'rest', 'graphql', 'v1', 'v2', 'v3',
            'mobile', 'app', 'web', 'admin', 'manage'
        ]
        
        for subdomain in api_subdomains:
            subdomain_url = f'https://{subdomain}.{domain}'
            
            try:
                # 检查子域名是否存在（模拟）
                if await self._check_subdomain_exists(subdomain_url):
                    # 在子域名上执行API发现
                    apis = await self._discover_apis_on_subdomain(subdomain_url)
                    discovered_apis.extend(apis)
            
            except Exception as e:
                logger.error(f"子域名枚举失败 {subdomain_url}: {e}")
        
        return discovered_apis
    
    async def _discover_by_documentation_parsing(self, domain: str) -> List[APIAsset]:
        """通过文档解析发现API"""
        logger.info(f"通过文档解析发现API: {domain}")
        
        discovered_apis = []
        
        # 常见的API文档路径
        doc_paths = [
            '/swagger.json', '/swagger.yaml',
            '/api-docs', '/api/docs',
            '/openapi.json', '/openapi.yaml',
            '/docs', '/documentation',
            '/api/swagger', '/api/openapi'
        ]
        
        for path in doc_paths:
            doc_url = f'https://{domain}{path}'
            
            try:
                # 尝试获取API文档
                doc_content = await self._fetch_api_documentation(doc_url)
                if doc_content:
                    # 解析文档中的API
                    apis = self._parse_api_documentation(doc_content, domain)
                    discovered_apis.extend(apis)
            
            except Exception as e:
                logger.error(f"文档解析失败 {doc_url}: {e}")
        
        return discovered_apis
    
    async def _discover_by_port_scanning(self, domain: str) -> List[APIAsset]:
        """通过端口扫描发现API"""
        logger.info(f"通过端口扫描发现API: {domain}")
        
        discovered_apis = []
        
        # 常见的API服务端口
        api_ports = [80, 443, 8080, 8443, 3000, 5000, 8000, 9000]
        
        for port in api_ports:
            try:
                # 检查端口是否开放（模拟）
                if await self._check_port_open(domain, port):
                    # 在该端口上发现API
                    apis = await self._discover_apis_on_port(domain, port)
                    discovered_apis.extend(apis)
            
            except Exception as e:
                logger.error(f"端口扫描失败 {domain}:{port}: {e}")
        
        return discovered_apis
    
    def _create_api_asset(
        self,
        url: str,
        method: str,
        discovery_method: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> APIAsset:
        """创建API资产对象"""
        parsed_url = urlparse(url)
        
        # 评估业务关键性
        business_criticality = self._assess_business_criticality(url)
        
        # 生成标签
        tags = self._generate_tags(url, method)
        
        return APIAsset(
            url=url,
            method=method,
            domain=parsed_url.netloc,
            path=parsed_url.path,
            discovery_method=discovery_method,
            discovery_time=datetime.now(),
            parameters=parameters or {},
            business_criticality=business_criticality,
            tags=tags
        )
    
    def _assess_business_criticality(self, url: str) -> str:
        """评估API的业务关键性"""
        url_lower = url.lower()
        
        for level, keywords in self.business_keywords.items():
            if any(keyword in url_lower for keyword in keywords):
                return level
        
        return 'medium'
    
    def _generate_tags(self, url: str, method: str) -> List[str]:
        """生成API标签"""
        tags = []
        url_lower = url.lower()
        
        # 基于URL路径的标签
        if '/admin' in url_lower:
            tags.append('admin')
        if '/api' in url_lower:
            tags.append('api')
        if '/user' in url_lower:
            tags.append('user')
        if '/product' in url_lower:
            tags.append('product')
        if '/order' in url_lower:
            tags.append('order')
        
        # 基于HTTP方法的标签
        if method in ['POST', 'PUT', 'PATCH']:
            tags.append('write_operation')
        elif method == 'DELETE':
            tags.append('delete_operation')
        else:
            tags.append('read_operation')
        
        return tags
    
    def _is_api_endpoint(self, url: str) -> bool:
        """判断URL是否是API端点"""
        api_indicators = [
            '/api/', '/v1/', '/v2/', '/v3/',
            '/rest/', '/graphql', '/service/',
            '.json', '.xml'
        ]
        
        url_lower = url.lower()
        return any(indicator in url_lower for indicator in api_indicators)
    
    def _should_exclude_url(self, url: str) -> bool:
        """判断是否应该排除该URL"""
        for pattern in self.exclusion_patterns:
            if re.match(pattern, url, re.IGNORECASE):
                return True
        return False
    
    def _extract_links_from_page(self, url: str) -> List[str]:
        """从页面中提取链接（模拟实现）"""
        # 在实际实现中，这里应该解析HTML页面
        # 模拟返回一些链接
        base_domain = urlparse(url).netloc
        return [
            f'https://{base_domain}/api/users',
            f'https://{base_domain}/api/products',
            f'https://{base_domain}/v1/orders'
        ]
    
    async def _check_subdomain_exists(self, subdomain_url: str) -> bool:
        """检查子域名是否存在（模拟实现）"""
        # 在实际实现中，这里应该进行DNS查询或HTTP请求
        return True  # 模拟返回
    
    async def _discover_apis_on_subdomain(self, subdomain_url: str) -> List[APIAsset]:
        """在子域名上发现API"""
        # 模拟在子域名上发现的API
        return [
            self._create_api_asset(f'{subdomain_url}/api/health', 'GET', 'subdomain_enumeration'),
            self._create_api_asset(f'{subdomain_url}/api/version', 'GET', 'subdomain_enumeration')
        ]
    
    async def _fetch_api_documentation(self, doc_url: str) -> Optional[str]:
        """获取API文档内容（模拟实现）"""
        # 在实际实现中，这里应该发送HTTP请求获取文档
        return None  # 模拟返回
    
    def _parse_api_documentation(self, doc_content: str, domain: str) -> List[APIAsset]:
        """解析API文档"""
        # 在实际实现中，这里应该解析Swagger/OpenAPI文档
        return []  # 模拟返回
    
    async def _check_port_open(self, domain: str, port: int) -> bool:
        """检查端口是否开放（模拟实现）"""
        # 在实际实现中，这里应该进行端口扫描
        return port in [80, 443]  # 模拟返回
    
    async def _discover_apis_on_port(self, domain: str, port: int) -> List[APIAsset]:
        """在指定端口上发现API"""
        # 模拟在端口上发现的API
        return [
            self._create_api_asset(f'https://{domain}:{port}/status', 'GET', 'port_scanning')
        ]
    
    async def _add_discovered_asset(self, asset: APIAsset):
        """添加发现的资产"""
        asset_key = f"{asset.method}:{asset.url}"
        
        if asset_key not in self.discovered_assets:
            self.discovered_assets[asset_key] = asset
            
            # 更新统计信息
            method = asset.discovery_method
            self.discovery_stats['by_method'][method] = \
                self.discovery_stats['by_method'].get(method, 0) + 1
            
            domain = asset.domain
            self.discovery_stats['by_domain'][domain] = \
                self.discovery_stats['by_domain'].get(domain, 0) + 1
    
    async def _post_process_assets(self):
        """后处理发现的资产"""
        logger.info("开始后处理API资产")
        
        # 1. 去重（已在添加时处理）
        
        # 2. 评估认证需求
        for asset in self.discovered_assets.values():
            asset.authentication_required = self._assess_authentication_requirement(asset)
        
        # 3. 检测速率限制
        for asset in self.discovered_assets.values():
            asset.rate_limit_info = await self._detect_rate_limiting(asset)
        
        logger.info("API资产后处理完成")
    
    def _assess_authentication_requirement(self, asset: APIAsset) -> bool:
        """评估API是否需要认证"""
        # 基于URL路径判断
        auth_indicators = ['/admin', '/user', '/account', '/profile', '/order']
        return any(indicator in asset.path.lower() for indicator in auth_indicators)
    
    async def _detect_rate_limiting(self, asset: APIAsset) -> Optional[Dict[str, Any]]:
        """检测API的速率限制（模拟实现）"""
        # 在实际实现中，这里应该发送多个请求来检测速率限制
        return {
            'has_rate_limit': True,
            'requests_per_minute': 60,
            'detection_method': 'header_analysis'
        }
    
    def get_discovery_statistics(self) -> Dict[str, Any]:
        """获取发现统计信息"""
        return self.discovery_stats.copy()
    
    def export_assets(self, format_type: str = 'json') -> str:
        """导出发现的资产"""
        assets_data = []
        
        for asset in self.discovered_assets.values():
            asset_dict = {
                'url': asset.url,
                'method': asset.method,
                'domain': asset.domain,
                'path': asset.path,
                'discovery_method': asset.discovery_method,
                'discovery_time': asset.discovery_time.isoformat(),
                'parameters': asset.parameters,
                'business_criticality': asset.business_criticality,
                'authentication_required': asset.authentication_required,
                'tags': asset.tags,
                'status': asset.status
            }
            assets_data.append(asset_dict)
        
        if format_type == 'json':
            return json.dumps(assets_data, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
