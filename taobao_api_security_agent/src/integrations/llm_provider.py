"""
LLM提供商集成模块

提供统一的LLM调用接口，支持多种大模型提供商。
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging

from ..utils.config_loader import get_config_loader
from ..utils.logger import get_logger

logger = get_logger(__name__)


class LLMResponse:
    """LLM响应模型"""
    
    def __init__(
        self,
        content: str,
        provider: str,
        model: str,
        usage: Optional[Dict[str, Any]] = None,
        response_time: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.content = content
        self.provider = provider
        self.model = model
        self.usage = usage or {}
        self.response_time = response_time
        self.metadata = metadata or {}
        self.timestamp = datetime.now()


class BaseLLMProvider(ABC):
    """LLM提供商基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider_name = self.__class__.__name__.replace('Provider', '').lower()
        self.model = config.get('model', '')
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', '')
        self.max_tokens = config.get('max_tokens', 4096)
        self.temperature = config.get('temperature', 0.1)
        self.timeout = config.get('timeout', 60)
        
        # 重试配置
        self.retry_config = config.get('retry', {})
        self.max_retries = self.retry_config.get('max_attempts', 3)
        self.backoff_factor = self.retry_config.get('backoff_factor', 2)
        
        # 速率限制
        self.rate_limit = config.get('rate_limit', {})
        self.requests_per_minute = self.rate_limit.get('requests_per_minute', 50)
        self.tokens_per_minute = self.rate_limit.get('tokens_per_minute', 40000)
        
        # 请求统计
        self.request_count = 0
        self.total_tokens = 0
        self.last_request_time = 0
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成响应"""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """验证配置"""
        pass
    
    async def generate_with_retry(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """带重试的响应生成"""
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                # 检查速率限制
                await self._check_rate_limit()
                
                # 生成响应
                response = await self.generate_response(messages, system_prompt, **kwargs)
                
                # 更新统计
                self._update_stats(response)
                
                return response
                
            except Exception as e:
                last_exception = e
                logger.warning(f"LLM请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    # 指数退避
                    delay = self.backoff_factor ** attempt
                    await asyncio.sleep(delay)
        
        # 所有重试都失败
        logger.error(f"LLM请求最终失败: {last_exception}")
        raise last_exception
    
    async def _check_rate_limit(self):
        """检查速率限制"""
        current_time = time.time()
        
        # 简单的速率限制检查
        if self.last_request_time > 0:
            time_diff = current_time - self.last_request_time
            min_interval = 60.0 / self.requests_per_minute
            
            if time_diff < min_interval:
                sleep_time = min_interval - time_diff
                logger.debug(f"速率限制，等待 {sleep_time:.2f} 秒")
                await asyncio.sleep(sleep_time)
        
        self.last_request_time = current_time
    
    def _update_stats(self, response: LLMResponse):
        """更新统计信息"""
        self.request_count += 1
        if response.usage:
            self.total_tokens += response.usage.get('total_tokens', 0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'provider': self.provider_name,
            'model': self.model,
            'request_count': self.request_count,
            'total_tokens': self.total_tokens,
            'average_tokens_per_request': self.total_tokens / max(self.request_count, 1)
        }


class ClaudeProvider(BaseLLMProvider):
    """Claude提供商"""
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成Claude响应"""
        try:
            import anthropic
            
            start_time = time.time()
            
            # 初始化客户端
            client = anthropic.AsyncAnthropic(api_key=self.api_key)
            
            # 构建消息
            claude_messages = []
            for msg in messages:
                claude_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # 调用API
            response = await client.messages.create(
                model=self.model,
                max_tokens=kwargs.get('max_tokens', self.max_tokens),
                temperature=kwargs.get('temperature', self.temperature),
                system=system_prompt or "",
                messages=claude_messages
            )
            
            response_time = time.time() - start_time
            
            return LLMResponse(
                content=response.content[0].text,
                provider="claude",
                model=self.model,
                usage={
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'total_tokens': response.usage.input_tokens + response.usage.output_tokens
                },
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"Claude API调用失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证Claude配置"""
        if not self.api_key:
            logger.error("Claude API密钥未配置")
            return False
        
        if not self.model:
            logger.error("Claude模型未配置")
            return False
        
        return True


class GeminiProvider(BaseLLMProvider):
    """Gemini提供商"""
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成Gemini响应"""
        try:
            import google.generativeai as genai
            
            start_time = time.time()
            
            # 配置API
            genai.configure(api_key=self.api_key)
            
            # 初始化模型
            model = genai.GenerativeModel(
                model_name=self.model,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=kwargs.get('max_tokens', self.max_tokens),
                    temperature=kwargs.get('temperature', self.temperature),
                )
            )
            
            # 构建对话内容
            conversation_text = ""
            if system_prompt:
                conversation_text += f"System: {system_prompt}\n\n"
            
            for msg in messages:
                role = "Human" if msg["role"] == "user" else "Assistant"
                conversation_text += f"{role}: {msg['content']}\n\n"
            
            # 生成响应
            response = await model.generate_content_async(conversation_text)
            
            response_time = time.time() - start_time
            
            return LLMResponse(
                content=response.text,
                provider="gemini",
                model=self.model,
                usage={
                    'total_tokens': response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0
                },
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"Gemini API调用失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证Gemini配置"""
        if not self.api_key:
            logger.error("Gemini API密钥未配置")
            return False
        
        if not self.model:
            logger.error("Gemini模型未配置")
            return False
        
        return True


class QwenProvider(BaseLLMProvider):
    """Qwen提供商"""
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成Qwen响应"""
        try:
            import aiohttp
            
            start_time = time.time()
            
            # 构建请求数据
            request_messages = []
            if system_prompt:
                request_messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            request_messages.extend(messages)
            
            request_data = {
                "model": self.model,
                "messages": request_messages,
                "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                "temperature": kwargs.get('temperature', self.temperature),
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=request_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
            
            response_time = time.time() - start_time
            
            return LLMResponse(
                content=result["choices"][0]["message"]["content"],
                provider="qwen",
                model=self.model,
                usage=result.get("usage", {}),
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"Qwen API调用失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证Qwen配置"""
        if not self.api_key:
            logger.error("Qwen API密钥未配置")
            return False
        
        if not self.model:
            logger.error("Qwen模型未配置")
            return False
        
        if not self.base_url:
            logger.error("Qwen API地址未配置")
            return False
        
        return True


class OpenAIProvider(BaseLLMProvider):
    """OpenAI提供商"""
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成OpenAI响应"""
        try:
            import openai
            
            start_time = time.time()
            
            # 初始化客户端
            client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            
            # 构建消息
            openai_messages = []
            if system_prompt:
                openai_messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            openai_messages.extend(messages)
            
            # 调用API
            response = await client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                max_tokens=kwargs.get('max_tokens', self.max_tokens),
                temperature=kwargs.get('temperature', self.temperature)
            )
            
            response_time = time.time() - start_time
            
            return LLMResponse(
                content=response.choices[0].message.content,
                provider="openai",
                model=self.model,
                usage={
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                },
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证OpenAI配置"""
        if not self.api_key:
            logger.error("OpenAI API密钥未配置")
            return False
        
        if not self.model:
            logger.error("OpenAI模型未配置")
            return False
        
        return True


class LLMProvider:
    """LLM提供商管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM提供商管理器
        
        Args:
            config: LLM配置
        """
        self.config = config or self._load_config()
        self.providers: Dict[str, BaseLLMProvider] = {}
        self.default_provider = self.config.get('default_provider', 'claude')
        
        # 初始化提供商
        self._initialize_providers()
        
        # 切换策略
        self.fallback_strategy = self.config.get('fallback_strategy', {})
        self.priority_order = self.fallback_strategy.get('priority_order', [])
    
    def _initialize_providers(self):
        """初始化所有提供商"""
        provider_configs = self.config.get('providers', {})
        
        provider_classes = {
            'claude': ClaudeProvider,
            'gemini': GeminiProvider,
            'qwen': QwenProvider,
            'openai': OpenAIProvider
        }
        
        for provider_name, provider_config in provider_configs.items():
            if not provider_config.get('enabled', False):
                continue
            
            if provider_name in provider_classes:
                try:
                    provider_class = provider_classes[provider_name]
                    provider = provider_class(provider_config)
                    
                    if provider.validate_config():
                        self.providers[provider_name] = provider
                        logger.info(f"初始化LLM提供商: {provider_name}")
                    else:
                        logger.warning(f"LLM提供商配置无效: {provider_name}")
                        
                except Exception as e:
                    logger.error(f"初始化LLM提供商失败: {provider_name}, 错误: {e}")
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        provider: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """
        生成响应
        
        Args:
            messages: 对话消息
            system_prompt: 系统提示词
            provider: 指定的提供商
            **kwargs: 其他参数
            
        Returns:
            LLM响应
        """
        # 确定使用的提供商
        target_provider = provider or self.default_provider
        
        # 尝试主要提供商
        if target_provider in self.providers:
            try:
                return await self.providers[target_provider].generate_with_retry(
                    messages, system_prompt, **kwargs
                )
            except Exception as e:
                logger.warning(f"主要提供商 {target_provider} 失败: {e}")
                
                # 如果启用了切换策略，尝试备用提供商
                if self.fallback_strategy.get('enabled', False):
                    return await self._try_fallback_providers(
                        messages, system_prompt, target_provider, **kwargs
                    )
                else:
                    raise
        else:
            raise ValueError(f"提供商不可用: {target_provider}")
    
    async def _try_fallback_providers(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str],
        failed_provider: str,
        **kwargs
    ) -> LLMResponse:
        """尝试备用提供商"""
        # 按优先级顺序尝试其他提供商
        for provider_name in self.priority_order:
            if provider_name == failed_provider or provider_name not in self.providers:
                continue
            
            try:
                logger.info(f"尝试备用提供商: {provider_name}")
                return await self.providers[provider_name].generate_with_retry(
                    messages, system_prompt, **kwargs
                )
            except Exception as e:
                logger.warning(f"备用提供商 {provider_name} 也失败: {e}")
                continue
        
        # 所有提供商都失败
        raise Exception("所有LLM提供商都不可用")
    
    def get_available_providers(self) -> List[str]:
        """获取可用的提供商列表"""
        return list(self.providers.keys())
    
    def get_provider_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有提供商的统计信息"""
        stats = {}
        for name, provider in self.providers.items():
            stats[name] = provider.get_stats()
        return stats
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_loader = get_config_loader()
            return config_loader.load_config('llm_config')
        except Exception as e:
            logger.error(f"加载LLM配置失败: {e}")
            return {}
