"""
会话管理模块

管理用户会话，包括Cookie管理、会话验证、会话轮换等功能。
"""

import json
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import requests
import logging
from pathlib import Path

from ..utils.config_loader import get_config_loader
from ..agent.state import SessionInfo

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化会话管理器
        
        Args:
            config: 会话管理配置
        """
        self.config = config or self._load_config()
        self.sessions: List[SessionInfo] = []
        self.current_session_index = 0
        self.session_stats = {}  # 会话使用统计
        
        # 加载会话配置
        self._load_sessions()
        
        # 验证配置
        self.validation_config = self.config.get('session_validation', {})
        self.rotation_config = self.config.get('session_rotation', {})
        
        # 最后验证时间
        self.last_validation_time = {}
    
    def get_current_session(self) -> Optional[SessionInfo]:
        """
        获取当前会话
        
        Returns:
            当前会话信息
        """
        if not self.sessions:
            logger.warning("没有可用的会话")
            return None
        
        session = self.sessions[self.current_session_index]
        
        # 检查会话是否有效
        if not self._is_session_valid(session):
            # 尝试切换到下一个有效会话
            valid_session = self._get_next_valid_session()
            if valid_session:
                return valid_session
            else:
                logger.error("没有有效的会话可用")
                return None
        
        return session
    
    def rotate_session(self) -> Optional[SessionInfo]:
        """
        轮换到下一个会话
        
        Returns:
            新的当前会话
        """
        if not self.sessions or len(self.sessions) <= 1:
            return self.get_current_session()
        
        rotation_method = self.rotation_config.get('rotation_strategy', {}).get('method', 'round_robin')
        
        if rotation_method == 'round_robin':
            self.current_session_index = (self.current_session_index + 1) % len(self.sessions)
        elif rotation_method == 'random':
            self.current_session_index = random.randint(0, len(self.sessions) - 1)
        elif rotation_method == 'weighted':
            self.current_session_index = self._weighted_selection()
        
        new_session = self.get_current_session()
        if new_session:
            logger.info(f"切换到会话: {new_session.user_id}")
        
        return new_session
    
    def validate_session(self, session: SessionInfo) -> bool:
        """
        验证会话有效性
        
        Args:
            session: 会话信息
            
        Returns:
            是否有效
        """
        if not self.validation_config.get('enabled', True):
            return True
        
        # 检查过期时间
        if session.expires_at and datetime.now() > session.expires_at:
            logger.warning(f"会话已过期: {session.user_id}")
            session.is_valid = False
            return False
        
        # 检查是否需要验证
        last_check = self.last_validation_time.get(session.user_id, 0)
        check_interval = self.validation_config.get('validation_strategy', {}).get('check_interval', 300)
        
        if time.time() - last_check < check_interval:
            return session.is_valid
        
        # 执行验证
        try:
            validation_result = self._perform_session_validation(session)
            session.is_valid = validation_result
            self.last_validation_time[session.user_id] = time.time()
            
            if validation_result:
                logger.debug(f"会话验证成功: {session.user_id}")
            else:
                logger.warning(f"会话验证失败: {session.user_id}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"会话验证异常: {session.user_id}, 错误: {e}")
            session.is_valid = False
            return False
    
    def refresh_session(self, session: SessionInfo) -> bool:
        """
        刷新会话
        
        Args:
            session: 会话信息
            
        Returns:
            刷新是否成功
        """
        refresh_strategy = self.validation_config.get('refresh_strategy', {})
        
        if not refresh_strategy.get('auto_refresh', True):
            return False
        
        try:
            refresh_method = refresh_strategy.get('refresh_method', 'api_call')
            
            if refresh_method == 'api_call':
                return self._refresh_session_via_api(session)
            elif refresh_method == 'cookie_update':
                return self._refresh_session_via_cookie_update(session)
            elif refresh_method == 're_login':
                return self._refresh_session_via_relogin(session)
            
            return False
            
        except Exception as e:
            logger.error(f"刷新会话失败: {session.user_id}, 错误: {e}")
            return False
    
    def get_session_headers(self, session: SessionInfo) -> Dict[str, str]:
        """
        获取会话的HTTP头部
        
        Args:
            session: 会话信息
            
        Returns:
            HTTP头部字典
        """
        headers = session.headers.copy()
        
        # 添加Cookie头
        if session.cookies:
            cookie_string = '; '.join([f"{k}={v}" for k, v in session.cookies.items()])
            headers['Cookie'] = cookie_string
        
        return headers
    
    def update_session_stats(self, session: SessionInfo, success: bool, response_time: float = None):
        """
        更新会话使用统计
        
        Args:
            session: 会话信息
            success: 请求是否成功
            response_time: 响应时间
        """
        user_id = session.user_id
        
        if user_id not in self.session_stats:
            self.session_stats[user_id] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'total_response_time': 0.0,
                'last_used': None
            }
        
        stats = self.session_stats[user_id]
        stats['total_requests'] += 1
        stats['last_used'] = datetime.now()
        
        if success:
            stats['successful_requests'] += 1
        else:
            stats['failed_requests'] += 1
        
        if response_time:
            stats['total_response_time'] += response_time
    
    def get_session_stats(self, user_id: str = None) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Args:
            user_id: 用户ID，如果为None则返回所有统计
            
        Returns:
            统计信息
        """
        if user_id:
            return self.session_stats.get(user_id, {})
        else:
            return self.session_stats.copy()
    
    def _load_sessions(self):
        """加载会话配置"""
        try:
            user_sessions_config = self.config.get('user_sessions', {})
            test_users = user_sessions_config.get('test_users', [])
            
            for user_config in test_users:
                session = SessionInfo(
                    user_id=user_config['user_id'],
                    user_type=user_config['user_type'],
                    cookies=user_config.get('cookies', {}),
                    headers=user_config.get('headers', {}),
                    expires_at=self._parse_datetime(user_config.get('expires_at')),
                    is_valid=True
                )
                self.sessions.append(session)
            
            # 添加匿名会话
            anonymous_config = user_sessions_config.get('anonymous_session', {})
            if anonymous_config.get('enabled', True):
                anonymous_session = SessionInfo(
                    user_id='anonymous',
                    user_type='anonymous',
                    cookies=anonymous_config.get('cookies', {}),
                    headers=anonymous_config.get('headers', {}),
                    is_valid=True
                )
                self.sessions.append(anonymous_session)
            
            logger.info(f"加载了 {len(self.sessions)} 个会话")
            
        except Exception as e:
            logger.error(f"加载会话配置失败: {e}")
    
    def _is_session_valid(self, session: SessionInfo) -> bool:
        """检查会话是否有效"""
        if not session.is_valid:
            return False
        
        # 检查过期时间
        if session.expires_at and datetime.now() > session.expires_at:
            return False
        
        return True
    
    def _get_next_valid_session(self) -> Optional[SessionInfo]:
        """获取下一个有效会话"""
        for _ in range(len(self.sessions)):
            self.current_session_index = (self.current_session_index + 1) % len(self.sessions)
            session = self.sessions[self.current_session_index]
            
            if self._is_session_valid(session):
                return session
        
        return None
    
    def _weighted_selection(self) -> int:
        """基于权重选择会话"""
        weights_config = self.rotation_config.get('rotation_strategy', {}).get('weights', {})
        
        total_weight = 0
        session_weights = []
        
        for session in self.sessions:
            weight = weights_config.get(session.user_type, 1)
            session_weights.append(weight)
            total_weight += weight
        
        if total_weight == 0:
            return random.randint(0, len(self.sessions) - 1)
        
        random_value = random.uniform(0, total_weight)
        current_weight = 0
        
        for i, weight in enumerate(session_weights):
            current_weight += weight
            if random_value <= current_weight:
                return i
        
        return len(self.sessions) - 1
    
    def _perform_session_validation(self, session: SessionInfo) -> bool:
        """执行会话验证"""
        validation_endpoints = self.validation_config.get('validation_strategy', {}).get('validation_endpoints', [])
        
        if not validation_endpoints:
            return True
        
        headers = self.get_session_headers(session)
        
        for endpoint_config in validation_endpoints:
            try:
                url = endpoint_config['url']
                method = endpoint_config.get('method', 'GET')
                expected_status = endpoint_config.get('expected_status', [200])
                expected_response_contains = endpoint_config.get('expected_response_contains', [])
                
                response = requests.request(
                    method=method,
                    url=url,
                    headers=headers,
                    timeout=10
                )
                
                # 检查状态码
                if response.status_code not in expected_status:
                    logger.warning(f"验证端点状态码不匹配: {response.status_code}")
                    return False
                
                # 检查响应内容
                if expected_response_contains:
                    response_text = response.text
                    for expected_content in expected_response_contains:
                        if expected_content not in response_text:
                            logger.warning(f"验证端点响应内容不匹配: {expected_content}")
                            return False
                
                # 如果有一个端点验证成功，就认为会话有效
                return True
                
            except Exception as e:
                logger.warning(f"验证端点请求失败: {endpoint_config['url']}, 错误: {e}")
                continue
        
        return False
    
    def _refresh_session_via_api(self, session: SessionInfo) -> bool:
        """通过API调用刷新会话"""
        # 这里应该实现具体的API刷新逻辑
        # 由于淘宝的具体刷新机制可能比较复杂，这里提供一个框架
        logger.info(f"尝试通过API刷新会话: {session.user_id}")
        return True
    
    def _refresh_session_via_cookie_update(self, session: SessionInfo) -> bool:
        """通过更新Cookie刷新会话"""
        logger.info(f"尝试通过Cookie更新刷新会话: {session.user_id}")
        return True
    
    def _refresh_session_via_relogin(self, session: SessionInfo) -> bool:
        """通过重新登录刷新会话"""
        logger.info(f"尝试通过重新登录刷新会话: {session.user_id}")
        return True
    
    def _parse_datetime(self, datetime_str: Optional[str]) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not datetime_str:
            return None
        
        try:
            return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        except ValueError:
            logger.warning(f"无法解析日期时间: {datetime_str}")
            return None
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_loader = get_config_loader()
            return config_loader.load_config('session_config')
        except Exception as e:
            logger.error(f"加载会话配置失败: {e}")
            return {}
