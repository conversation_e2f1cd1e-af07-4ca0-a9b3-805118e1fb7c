"""
淘宝签名服务集成模块

处理与淘宝签名服务的集成，生成和验证请求签名。
"""

import hashlib
import hmac
import time
import json
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode, quote
import requests
import logging
from datetime import datetime

from ..utils.config_loader import get_config_loader
from ..agent.state import SignatureInfo

logger = logging.getLogger(__name__)


class SignatureService:
    """淘宝签名服务"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化签名服务
        
        Args:
            config: 签名服务配置
        """
        self.config = config or self._load_config()
        self.cache = {}  # 简单的内存缓存
        
        # 服务端点配置
        self.primary_endpoint = self.config.get('signature_service', {}).get('endpoints', {}).get('primary', {})
        self.fallback_endpoint = self.config.get('signature_service', {}).get('endpoints', {}).get('fallback', {})
        
        # 认证配置
        self.auth_config = self.config.get('signature_service', {}).get('authentication', {})
        
        # 算法配置
        self.algorithms = self.config.get('signature_algorithms', {})
        self.default_algorithm = self.algorithms.get('default', 'hmac_sha256')
        
        # 缓存配置
        self.cache_config = self.config.get('cache', {})
        self.cache_enabled = self.cache_config.get('enabled', True)
        self.cache_ttl = self.cache_config.get('ttl', 300)
    
    def generate_signature(
        self,
        url: str,
        method: str = 'GET',
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        body: Optional[str] = None,
        algorithm: Optional[str] = None
    ) -> SignatureInfo:
        """
        生成请求签名
        
        Args:
            url: 请求URL
            method: HTTP方法
            params: 请求参数
            headers: 请求头
            body: 请求体
            algorithm: 签名算法
            
        Returns:
            签名信息
        """
        algorithm = algorithm or self.default_algorithm
        
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(url, method, params, headers, body, algorithm)
            if self.cache_enabled and cache_key in self.cache:
                cached_result = self.cache[cache_key]
                if self._is_cache_valid(cached_result):
                    logger.debug(f"使用缓存的签名: {cache_key}")
                    return cached_result['signature_info']
            
            # 生成签名
            if algorithm == 'hmac_sha256':
                signature_info = self._generate_hmac_sha256_signature(url, method, params, headers, body)
            elif algorithm == 'md5':
                signature_info = self._generate_md5_signature(url, method, params, headers, body)
            elif algorithm == 'custom_taobao':
                signature_info = self._generate_custom_taobao_signature(url, method, params, headers, body)
            else:
                # 尝试调用远程签名服务
                signature_info = self._call_remote_signature_service(url, method, params, headers, body, algorithm)
            
            # 缓存结果
            if self.cache_enabled:
                self.cache[cache_key] = {
                    'signature_info': signature_info,
                    'timestamp': time.time()
                }
            
            logger.info(f"成功生成签名: {algorithm}")
            return signature_info
            
        except Exception as e:
            logger.error(f"生成签名失败: {e}")
            raise
    
    def verify_signature(
        self,
        signature: str,
        url: str,
        method: str = 'GET',
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        body: Optional[str] = None,
        algorithm: Optional[str] = None
    ) -> bool:
        """
        验证签名
        
        Args:
            signature: 待验证的签名
            url: 请求URL
            method: HTTP方法
            params: 请求参数
            headers: 请求头
            body: 请求体
            algorithm: 签名算法
            
        Returns:
            验证结果
        """
        try:
            # 重新生成签名进行比较
            signature_info = self.generate_signature(url, method, params, headers, body, algorithm)
            return signature_info.signature == signature
            
        except Exception as e:
            logger.error(f"验证签名失败: {e}")
            return False
    
    def reconstruct_request(
        self,
        original_url: str,
        original_method: str = 'GET',
        original_params: Optional[Dict[str, Any]] = None,
        original_headers: Optional[Dict[str, str]] = None,
        original_body: Optional[str] = None,
        new_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        重构请求并生成新签名
        
        Args:
            original_url: 原始URL
            original_method: 原始HTTP方法
            original_params: 原始参数
            original_headers: 原始头部
            original_body: 原始请求体
            new_params: 新参数（用于测试）
            
        Returns:
            重构后的请求信息
        """
        try:
            # 合并参数
            merged_params = (original_params or {}).copy()
            if new_params:
                merged_params.update(new_params)
            
            # 处理系统参数
            merged_params = self._add_system_params(merged_params)
            
            # 生成新签名
            signature_info = self.generate_signature(
                original_url, original_method, merged_params, original_headers, original_body
            )
            
            # 添加签名到参数中
            merged_params['sign'] = signature_info.signature
            
            # 重构请求
            reconstructed_request = {
                'url': original_url,
                'method': original_method,
                'params': merged_params,
                'headers': self._update_headers(original_headers or {}),
                'body': original_body,
                'signature_info': signature_info
            }
            
            logger.info("成功重构请求")
            return reconstructed_request
            
        except Exception as e:
            logger.error(f"重构请求失败: {e}")
            raise
    
    def _generate_hmac_sha256_signature(
        self,
        url: str,
        method: str,
        params: Optional[Dict[str, Any]],
        headers: Optional[Dict[str, str]],
        body: Optional[str]
    ) -> SignatureInfo:
        """生成HMAC-SHA256签名"""
        algorithm_config = next(
            (alg for alg in self.algorithms.get('supported', []) if alg['name'] == 'hmac_sha256'),
            {}
        )
        
        secret_key = algorithm_config.get('parameters', {}).get('secret_key', '')
        if not secret_key:
            raise ValueError("HMAC-SHA256密钥未配置")
        
        # 构建签名字符串
        sign_string = self._build_sign_string(url, method, params, headers, body)
        
        # 生成签名
        signature = hmac.new(
            secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return SignatureInfo(
            algorithm='hmac_sha256',
            signature=signature,
            parameters={'sign_string': sign_string},
            timestamp=datetime.now()
        )
    
    def _generate_md5_signature(
        self,
        url: str,
        method: str,
        params: Optional[Dict[str, Any]],
        headers: Optional[Dict[str, str]],
        body: Optional[str]
    ) -> SignatureInfo:
        """生成MD5签名"""
        algorithm_config = next(
            (alg for alg in self.algorithms.get('supported', []) if alg['name'] == 'md5'),
            {}
        )
        
        secret_key = algorithm_config.get('parameters', {}).get('secret_key', '')
        if not secret_key:
            raise ValueError("MD5密钥未配置")
        
        # 构建签名字符串
        sign_string = self._build_sign_string(url, method, params, headers, body)
        sign_string_with_key = sign_string + secret_key
        
        # 生成MD5签名
        signature = hashlib.md5(sign_string_with_key.encode('utf-8')).hexdigest()
        
        return SignatureInfo(
            algorithm='md5',
            signature=signature,
            parameters={'sign_string': sign_string},
            timestamp=datetime.now()
        )
    
    def _generate_custom_taobao_signature(
        self,
        url: str,
        method: str,
        params: Optional[Dict[str, Any]],
        headers: Optional[Dict[str, str]],
        body: Optional[str]
    ) -> SignatureInfo:
        """生成淘宝自定义签名"""
        algorithm_config = next(
            (alg for alg in self.algorithms.get('supported', []) if alg['name'] == 'custom_taobao'),
            {}
        )
        
        app_secret = algorithm_config.get('parameters', {}).get('app_secret', '')
        if not app_secret:
            raise ValueError("淘宝App Secret未配置")
        
        # 淘宝特定的签名逻辑
        sorted_params = self._sort_params(params or {})
        
        # 构建签名字符串（淘宝格式）
        sign_string = app_secret
        for key, value in sorted_params.items():
            if key != 'sign':
                sign_string += f"{key}{value}"
        sign_string += app_secret
        
        # 生成签名
        signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
        
        return SignatureInfo(
            algorithm='custom_taobao',
            signature=signature,
            parameters={'sign_string': sign_string},
            timestamp=datetime.now()
        )
    
    def _call_remote_signature_service(
        self,
        url: str,
        method: str,
        params: Optional[Dict[str, Any]],
        headers: Optional[Dict[str, str]],
        body: Optional[str],
        algorithm: str
    ) -> SignatureInfo:
        """调用远程签名服务"""
        service_url = self.primary_endpoint.get('url')
        if not service_url:
            raise ValueError("签名服务URL未配置")
        
        # 构建请求数据
        request_data = {
            'url': url,
            'method': method,
            'params': params or {},
            'headers': headers or {},
            'body': body,
            'algorithm': algorithm
        }
        
        # 添加认证头
        request_headers = {}
        if self.auth_config.get('auth_method') == 'header':
            auth_header = self.auth_config.get('auth_header', 'X-API-Key')
            api_key = self.auth_config.get('api_key')
            if api_key:
                request_headers[auth_header] = api_key
        
        try:
            # 调用主服务
            response = requests.post(
                service_url,
                json=request_data,
                headers=request_headers,
                timeout=self.primary_endpoint.get('timeout', 10)
            )
            response.raise_for_status()
            
            result = response.json()
            
            return SignatureInfo(
                algorithm=algorithm,
                signature=result['signature'],
                parameters=result.get('parameters', {}),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.warning(f"主签名服务调用失败: {e}")
            
            # 尝试备用服务
            fallback_url = self.fallback_endpoint.get('url')
            if fallback_url:
                try:
                    response = requests.post(
                        fallback_url,
                        json=request_data,
                        headers=request_headers,
                        timeout=self.fallback_endpoint.get('timeout', 15)
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    
                    return SignatureInfo(
                        algorithm=algorithm,
                        signature=result['signature'],
                        parameters=result.get('parameters', {}),
                        timestamp=datetime.now()
                    )
                    
                except Exception as fallback_error:
                    logger.error(f"备用签名服务也失败: {fallback_error}")
            
            raise Exception(f"所有签名服务都不可用: {e}")
    
    def _build_sign_string(
        self,
        url: str,
        method: str,
        params: Optional[Dict[str, Any]],
        headers: Optional[Dict[str, str]],
        body: Optional[str]
    ) -> str:
        """构建签名字符串"""
        # 排序参数
        sorted_params = self._sort_params(params or {})
        
        # 构建查询字符串
        query_string = urlencode(sorted_params, quote_via=quote)
        
        # 构建签名字符串
        sign_string = f"{method.upper()}&{quote(url, safe='')}&{quote(query_string, safe='')}"
        
        return sign_string
    
    def _sort_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """排序参数"""
        # 移除签名参数
        filtered_params = {k: v for k, v in params.items() if k != 'sign'}
        
        # 按键名排序
        return dict(sorted(filtered_params.items()))
    
    def _add_system_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """添加系统参数"""
        system_params_config = self.config.get('signature_parameters', {}).get('system_params', {})
        
        # 添加时间戳
        if 'timestamp' in system_params_config:
            timestamp_config = system_params_config['timestamp']
            if timestamp_config.get('auto_generate', True):
                if timestamp_config.get('format') == 'unix':
                    params['timestamp'] = str(int(time.time()))
                else:
                    params['timestamp'] = datetime.now().isoformat()
        
        # 添加其他系统参数
        for param_name, param_config in system_params_config.items():
            if param_name != 'timestamp' and param_name not in params:
                if 'default' in param_config:
                    params[param_name] = param_config['default']
        
        return params
    
    def _update_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """更新请求头"""
        headers_config = self.config.get('request_reconstruction', {}).get('headers', {})
        
        # 更新指定的头部
        update_headers = headers_config.get('update_headers', {})
        headers.update(update_headers)
        
        # 移除指定的头部
        remove_headers = headers_config.get('remove_headers', [])
        for header_name in remove_headers:
            headers.pop(header_name, None)
        
        return headers
    
    def _generate_cache_key(self, *args) -> str:
        """生成缓存键"""
        key_data = json.dumps(args, sort_keys=True, default=str)
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cached_result: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        if not self.cache_enabled:
            return False
        
        cache_time = cached_result.get('timestamp', 0)
        return (time.time() - cache_time) < self.cache_ttl
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_loader = get_config_loader()
            return config_loader.load_config('signature_config')
        except Exception as e:
            logger.error(f"加载签名配置失败: {e}")
            return {}
