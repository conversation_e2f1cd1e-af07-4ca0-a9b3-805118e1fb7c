"""
Agent状态定义模块

定义Agent工作流中使用的状态结构和数据模型。
"""

from typing import Dict, List, Optional, Any, Union, Literal
from typing_extensions import TypedDict, Annotated
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import operator


class RiskLevel(str, Enum):
    """风险等级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class VulnerabilityType(str, Enum):
    """漏洞类型枚举"""
    FIELD_EXPOSURE = "field_exposure"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    ANTI_CRAWLER_BYPASS = "anti_crawler_bypass"
    OWASP_API1_BOLA = "owasp_api1_bola"
    OWASP_API2_BROKEN_AUTH = "owasp_api2_broken_auth"
    OWASP_API3_BROKEN_OBJ_PROP = "owasp_api3_broken_obj_prop"
    OWASP_API4_UNRESTRICTED = "owasp_api4_unrestricted"
    OWASP_API5_BFLA = "owasp_api5_bfla"
    OWASP_API6_UNRESTRICTED_ACCESS = "owasp_api6_unrestricted_access"
    OWASP_API7_SSRF = "owasp_api7_ssrf"
    OWASP_API8_SECURITY_MISCONFIGURATION = "owasp_api8_security_misconfiguration"
    OWASP_API9_IMPROPER_INVENTORY = "owasp_api9_improper_inventory"
    OWASP_API10_UNSAFE_CONSUMPTION = "owasp_api10_unsafe_consumption"


class InputType(str, Enum):
    """输入数据类型枚举"""
    TRAFFIC_DATA = "traffic_data"
    API_LIST = "api_list"
    WEB_DATA = "web_data"
    HAR_FILE = "har_file"
    PCAP_FILE = "pcap_file"


class APIInfo(BaseModel):
    """API信息模型"""
    url: str = Field(..., description="API URL")
    method: str = Field(..., description="HTTP方法")
    domain: str = Field(..., description="域名")
    path: str = Field(..., description="路径")
    
    # 请求信息
    request_headers: Dict[str, str] = Field(default_factory=dict, description="请求头")
    request_body: Optional[str] = Field(None, description="请求体")
    request_params: Dict[str, Any] = Field(default_factory=dict, description="请求参数")
    
    # 响应信息
    response_status: Optional[int] = Field(None, description="响应状态码")
    response_headers: Dict[str, str] = Field(default_factory=dict, description="响应头")
    response_body: Optional[str] = Field(None, description="响应体")
    response_time: Optional[float] = Field(None, description="响应时间(毫秒)")
    
    # 元数据
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    source: Optional[str] = Field(None, description="数据来源")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SecurityCheckResult(BaseModel):
    """安全检查结果模型"""
    check_type: VulnerabilityType = Field(..., description="检查类型")
    risk_level: RiskLevel = Field(..., description="风险等级")
    
    # 检查结果
    is_vulnerable: bool = Field(..., description="是否存在漏洞")
    title: str = Field(..., description="问题标题")
    description: str = Field(..., description="问题描述")
    
    # 技术细节
    technical_details: Dict[str, Any] = Field(default_factory=dict, description="技术细节")
    evidence: List[str] = Field(default_factory=list, description="证据")
    
    # 修复建议
    recommendation: str = Field("", description="修复建议")
    references: List[str] = Field(default_factory=list, description="参考链接")
    
    # 元数据
    api_info: APIInfo = Field(..., description="相关API信息")
    check_timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    llm_analysis: Optional[str] = Field(None, description="LLM分析结果")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SessionInfo(BaseModel):
    """会话信息模型"""
    user_id: str = Field(..., description="用户ID")
    user_type: str = Field(..., description="用户类型")
    cookies: Dict[str, str] = Field(default_factory=dict, description="Cookie信息")
    headers: Dict[str, str] = Field(default_factory=dict, description="请求头")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    is_valid: bool = Field(True, description="是否有效")


class SignatureInfo(BaseModel):
    """签名信息模型"""
    algorithm: str = Field(..., description="签名算法")
    signature: str = Field(..., description="签名值")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="签名参数")
    timestamp: datetime = Field(default_factory=datetime.now, description="生成时间")


class AgentState(TypedDict):
    """Agent状态定义"""
    
    # 输入数据
    input_data: str  # 原始输入数据
    input_type: InputType  # 输入类型
    
    # 解析后的API信息
    apis: Annotated[List[APIInfo], operator.add]  # API列表，支持累加
    
    # 当前处理的API
    current_api: Optional[APIInfo]
    
    # 会话管理
    current_session: Optional[SessionInfo]
    available_sessions: List[SessionInfo]
    
    # 签名信息
    signature_info: Optional[SignatureInfo]
    
    # 安全检查结果
    security_results: Annotated[List[SecurityCheckResult], operator.add]  # 安全检查结果，支持累加
    
    # 处理状态
    processing_stage: Literal[
        "input_parsing",
        "api_extraction", 
        "session_setup",
        "signature_generation",
        "security_testing",
        "llm_analysis",
        "report_generation",
        "completed",
        "error"
    ]
    
    # 错误信息
    errors: Annotated[List[str], operator.add]  # 错误列表，支持累加
    
    # 配置信息
    config: Dict[str, Any]
    
    # LLM分析结果
    llm_analysis_results: Annotated[List[Dict[str, Any]], operator.add]
    
    # 最终报告
    final_report: Optional[Dict[str, Any]]
    
    # 元数据
    start_time: datetime
    end_time: Optional[datetime]
    total_apis_processed: int
    total_vulnerabilities_found: int


class AnalysisContext(BaseModel):
    """分析上下文模型"""
    target_domain: str = Field(..., description="目标域名")
    analysis_mode: Literal["comprehensive", "fast", "custom"] = Field("comprehensive", description="分析模式")
    enabled_checks: List[VulnerabilityType] = Field(default_factory=list, description="启用的检查项")
    max_concurrent_requests: int = Field(10, description="最大并发请求数")
    request_timeout: int = Field(30, description="请求超时时间")
    
    # LLM配置
    llm_provider: str = Field("claude", description="LLM提供商")
    llm_model: str = Field("claude-3-5-sonnet-20241022", description="LLM模型")
    
    # 输出配置
    output_formats: List[str] = Field(default_factory=lambda: ["json", "html"], description="输出格式")
    include_raw_data: bool = Field(False, description="是否包含原始数据")


class WorkflowConfig(BaseModel):
    """工作流配置模型"""
    # 节点配置
    enable_parallel_processing: bool = Field(True, description="是否启用并行处理")
    max_retries: int = Field(3, description="最大重试次数")
    retry_delay: float = Field(1.0, description="重试延迟(秒)")
    
    # 条件分支配置
    skip_on_error: bool = Field(False, description="出错时是否跳过")
    continue_on_partial_failure: bool = Field(True, description="部分失败时是否继续")
    
    # 资源限制
    memory_limit_mb: int = Field(1024, description="内存限制(MB)")
    execution_timeout: int = Field(3600, description="执行超时时间(秒)")


# 工具函数
def create_initial_state(
    input_data: str,
    input_type: InputType,
    config: Dict[str, Any]
) -> AgentState:
    """创建初始状态"""
    return AgentState(
        input_data=input_data,
        input_type=input_type,
        apis=[],
        current_api=None,
        current_session=None,
        available_sessions=[],
        signature_info=None,
        security_results=[],
        processing_stage="input_parsing",
        errors=[],
        config=config,
        llm_analysis_results=[],
        final_report=None,
        start_time=datetime.now(),
        end_time=None,
        total_apis_processed=0,
        total_vulnerabilities_found=0
    )


def update_processing_stage(
    state: AgentState,
    new_stage: Literal[
        "input_parsing",
        "api_extraction",
        "session_setup", 
        "signature_generation",
        "security_testing",
        "llm_analysis",
        "report_generation",
        "completed",
        "error"
    ]
) -> Dict[str, Any]:
    """更新处理阶段"""
    return {"processing_stage": new_stage}


def add_error(state: AgentState, error_message: str) -> Dict[str, Any]:
    """添加错误信息"""
    return {"errors": [error_message]}


def add_security_result(state: AgentState, result: SecurityCheckResult) -> Dict[str, Any]:
    """添加安全检查结果"""
    return {"security_results": [result]}
