"""
主Agent工作流

使用LangGraph框架构建的淘宝API安全检测Agent主工作流。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
import asyncio

from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool

from .state import AgentState, create_initial_state, InputType, APIInfo, SecurityCheckResult
from ..parsers import TrafficDataParser, APIListParser, WebDataParser
from ..integrations import SignatureService, SessionManager
from ..security import (
    RequestReplayer, ResponseAnalyzer, ParameterFuzzer,
    FieldExposureDetector, PrivilegeEscalationDetector, AntiCrawlerDetector
)
from ..security.intelligent_analyzer import IntelligentSecurityAnalyzer
from ..reporting.report_generator import ReportGenerator
from ..utils.config_loader import get_config_loader
from ..utils.logger import get_logger

logger = get_logger(__name__)


class TaobaoAPISecurityAgent:
    """淘宝API安全检测Agent"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化Agent
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_loader = get_config_loader(config_dir)
        self.config = self._load_all_configs()
        
        # 初始化组件
        self.traffic_parser = TrafficDataParser()
        self.api_list_parser = APIListParser()
        self.web_data_parser = WebDataParser()
        self.signature_service = SignatureService(self.config.get('signature_config'))
        self.session_manager = SessionManager(self.config.get('session_config'))

        # 初始化安全检测组件
        self.request_replayer = RequestReplayer(
            self.signature_service, self.session_manager, self.config.get('agent_config')
        )
        self.response_analyzer = ResponseAnalyzer(
            self.config.get('agent_config', {}).get('security_checks', {}).get('field_exposure', {})
        )
        self.parameter_fuzzer = ParameterFuzzer(
            self.config.get('agent_config', {}).get('security_checks', {}).get('owasp_fuzzer', {})
        )

        # 初始化具体检测器
        self.field_exposure_detector = FieldExposureDetector(
            self.request_replayer, self.response_analyzer, self.parameter_fuzzer,
            self.config.get('agent_config', {}).get('security_checks', {}).get('field_exposure', {})
        )
        self.privilege_escalation_detector = PrivilegeEscalationDetector(
            self.request_replayer, self.response_analyzer,
            self.config.get('agent_config', {}).get('security_checks', {}).get('privilege_escalation', {})
        )
        self.anti_crawler_detector = AntiCrawlerDetector(
            self.request_replayer,
            self.config.get('agent_config', {}).get('security_checks', {}).get('anti_crawler', {})
        )

        # 初始化LLM和智能分析组件
        from ..integrations.llm_provider import LLMProvider
        self.llm_provider = LLMProvider(self.config.get('llm_config'))
        self.intelligent_analyzer = IntelligentSecurityAnalyzer(
            self.llm_provider,
            self.config.get('agent_config', {}).get('intelligent_analysis', {})
        )
        self.report_generator = ReportGenerator(
            self.llm_provider, self.intelligent_analyzer,
            self.config.get('agent_config', {}).get('reporting', {})
        )
        
        # 构建工作流图
        self.graph = self._build_graph()
        
        logger.info("淘宝API安全检测Agent初始化完成")
    
    def analyze(
        self,
        input_data: str,
        input_type: InputType = InputType.API_LIST,
        analysis_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        执行API安全分析
        
        Args:
            input_data: 输入数据
            input_type: 输入数据类型
            analysis_config: 分析配置
            
        Returns:
            分析结果
        """
        try:
            # 创建初始状态
            config = self.config.copy()
            if analysis_config:
                config.update(analysis_config)
            
            initial_state = create_initial_state(input_data, input_type, config)
            
            # 执行工作流
            logger.info(f"开始执行API安全分析，输入类型: {input_type}")
            result = self.graph.invoke(initial_state)
            
            logger.info("API安全分析完成")
            return result
            
        except Exception as e:
            logger.error(f"API安全分析失败: {e}")
            raise
    
    def analyze_api_list(self, api_urls: List[str]) -> Dict[str, Any]:
        """
        分析API列表
        
        Args:
            api_urls: API URL列表
            
        Returns:
            分析结果
        """
        # 将URL列表转换为JSON格式
        api_list_data = []
        for url in api_urls:
            api_list_data.append({"url": url, "method": "GET"})
        
        import json
        input_data = json.dumps(api_list_data)
        
        return self.analyze(input_data, InputType.API_LIST)
    
    def analyze_har_file(self, har_content: str) -> Dict[str, Any]:
        """
        分析HAR文件
        
        Args:
            har_content: HAR文件内容
            
        Returns:
            分析结果
        """
        return self.analyze(har_content, InputType.TRAFFIC_DATA)
    
    def analyze_web_page(self, html_content: str, base_url: str = None) -> Dict[str, Any]:
        """
        分析网页内容
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            
        Returns:
            分析结果
        """
        # 可以在配置中传递base_url
        analysis_config = {"base_url": base_url} if base_url else {}
        return self.analyze(html_content, InputType.WEB_DATA, analysis_config)
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流图"""
        # 创建状态图
        graph = StateGraph(AgentState)

        # 添加节点
        graph.add_node("parse_input", self._parse_input_node)
        graph.add_node("setup_session", self._setup_session_node)
        graph.add_node("security_check", self._security_check_node_wrapper)
        graph.add_node("intelligent_analysis", self._intelligent_analysis_node_wrapper)
        graph.add_node("generate_report", self._generate_report_node)

        # 定义边
        graph.add_edge(START, "parse_input")
        graph.add_edge("parse_input", "setup_session")
        graph.add_edge("setup_session", "security_check")
        graph.add_edge("security_check", "intelligent_analysis")
        graph.add_edge("intelligent_analysis", "generate_report")
        graph.add_edge("generate_report", END)

        # 编译图
        return graph.compile()

    def _security_check_node_wrapper(self, state: AgentState) -> Dict[str, Any]:
        """安全检查节点包装器（同步版本）"""
        import asyncio

        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self._security_check_node(state))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"安全检查包装器失败: {e}")
            return {
                "errors": [f"安全检查包装器失败: {str(e)}"],
                "processing_stage": "error"
            }

    def _intelligent_analysis_node_wrapper(self, state: AgentState) -> Dict[str, Any]:
        """智能分析节点包装器（同步版本）"""
        import asyncio

        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self._intelligent_analysis_node(state))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"智能分析包装器失败: {e}")
            return {
                "errors": [f"智能分析包装器失败: {str(e)}"],
                "processing_stage": "error"
            }
    
    def _parse_input_node(self, state: AgentState) -> Dict[str, Any]:
        """解析输入数据节点"""
        try:
            logger.info(f"解析输入数据，类型: {state['input_type']}")
            
            input_data = state["input_data"]
            input_type = state["input_type"]
            
            apis = []
            
            if input_type == InputType.TRAFFIC_DATA:
                apis = self.traffic_parser.parse(input_data)
            elif input_type == InputType.API_LIST:
                apis = self.api_list_parser.parse(input_data)
            elif input_type == InputType.WEB_DATA:
                base_url = state["config"].get("base_url")
                apis = self.web_data_parser.parse(input_data, base_url)
            else:
                raise ValueError(f"不支持的输入类型: {input_type}")
            
            logger.info(f"解析出 {len(apis)} 个API")
            
            return {
                "apis": apis,
                "processing_stage": "api_extraction",
                "total_apis_processed": len(apis)
            }
            
        except Exception as e:
            logger.error(f"解析输入数据失败: {e}")
            return {
                "errors": [f"解析输入数据失败: {str(e)}"],
                "processing_stage": "error"
            }
    
    def _setup_session_node(self, state: AgentState) -> Dict[str, Any]:
        """设置会话节点"""
        try:
            logger.info("设置会话")
            
            # 获取当前会话
            current_session = self.session_manager.get_current_session()
            if not current_session:
                logger.warning("没有可用的会话，使用匿名模式")
            
            # 获取所有可用会话
            available_sessions = self.session_manager.sessions
            
            return {
                "current_session": current_session,
                "available_sessions": available_sessions,
                "processing_stage": "session_setup"
            }
            
        except Exception as e:
            logger.error(f"设置会话失败: {e}")
            return {
                "errors": [f"设置会话失败: {str(e)}"],
                "processing_stage": "error"
            }
    
    async def _security_check_node(self, state: AgentState) -> Dict[str, Any]:
        """安全检查节点"""
        try:
            logger.info("执行安全检查")

            apis = state["apis"]
            available_sessions = state["available_sessions"]
            security_config = state["config"].get("security_checks", {})
            security_results = []

            # 执行各种安全检测
            for api in apis:
                try:
                    # 1. 字段过度透出检测
                    if security_config.get("field_exposure", {}).get("enabled", True):
                        field_results = await self.field_exposure_detector.detect_field_exposure(
                            api, available_sessions
                        )
                        security_results.extend(field_results)

                    # 2. 越权访问检测
                    if security_config.get("privilege_escalation", {}).get("enabled", True):
                        privilege_results = await self.privilege_escalation_detector.detect_privilege_escalation(
                            api, available_sessions
                        )
                        security_results.extend(privilege_results)

                    # 3. 反爬策略检测
                    if security_config.get("anti_crawler", {}).get("enabled", True):
                        current_session = available_sessions[0] if available_sessions else None
                        anti_crawler_results = await self.anti_crawler_detector.detect_anti_crawler_measures(
                            api, current_session
                        )
                        security_results.extend(anti_crawler_results)

                    logger.info(f"API {api.url} 安全检查完成，发现 {len(security_results)} 个问题")

                except Exception as e:
                    logger.error(f"API {api.url} 安全检查失败: {e}")
                    continue

            logger.info(f"完成所有安全检查，总共发现 {len(security_results)} 个安全问题")

            return {
                "security_results": security_results,
                "processing_stage": "security_testing",
                "total_vulnerabilities_found": len(security_results)
            }

        except Exception as e:
            logger.error(f"安全检查失败: {e}")
            return {
                "errors": [f"安全检查失败: {str(e)}"],
                "processing_stage": "error"
            }

    async def _intelligent_analysis_node(self, state: AgentState) -> Dict[str, Any]:
        """智能分析节点"""
        try:
            logger.info("开始智能分析")

            apis = state["apis"]
            security_results = state["security_results"]
            available_sessions = state["available_sessions"]
            config = state["config"]

            # 检查是否启用智能分析
            intelligent_config = config.get("intelligent_analysis", {})
            if not intelligent_config.get("enabled", True):
                logger.info("智能分析已禁用，跳过")
                return {
                    "intelligent_analysis_results": {},
                    "processing_stage": "intelligent_analysis_skipped"
                }

            # 检查LLM提供商是否可用
            available_providers = self.llm_provider.get_available_providers()
            if not available_providers:
                logger.warning("没有可用的LLM提供商，跳过智能分析")
                return {
                    "intelligent_analysis_results": {},
                    "processing_stage": "intelligent_analysis_skipped",
                    "skip_reason": "no_llm_providers"
                }

            intelligent_results = {}

            # 对每个API进行智能分析
            for api in apis:
                try:
                    # 获取该API相关的安全结果
                    api_security_results = [
                        result for result in security_results
                        if result.api_info.url == api.url
                    ]

                    if not api_security_results:
                        continue

                    logger.info(f"对API {api.url} 进行智能分析")

                    # 执行智能分析
                    analysis_result = await self.intelligent_analyzer.analyze_security_results(
                        api_security_results, api, {
                            "available_sessions": len(available_sessions),
                            "analysis_config": intelligent_config
                        }
                    )

                    if analysis_result:
                        intelligent_results[api.url] = analysis_result

                except Exception as e:
                    logger.error(f"API {api.url} 智能分析失败: {e}")
                    continue

            # 生成综合安全总结
            try:
                if security_results:
                    logger.info("生成综合安全总结")
                    comprehensive_summary = await self.intelligent_analyzer.generate_security_summary(
                        security_results, apis, {
                            "analysis_config": intelligent_config,
                            "session_info": {
                                "total_sessions": len(available_sessions),
                                "session_types": [s.user_type for s in available_sessions]
                            }
                        }
                    )
                    intelligent_results["comprehensive_summary"] = comprehensive_summary
            except Exception as e:
                logger.error(f"生成综合安全总结失败: {e}")

            logger.info(f"智能分析完成，分析了 {len(intelligent_results)} 个对象")

            return {
                "intelligent_analysis_results": intelligent_results,
                "processing_stage": "intelligent_analysis_completed",
                "analyzed_apis_count": len([k for k in intelligent_results.keys() if k != "comprehensive_summary"]),
                "llm_provider_stats": self.llm_provider.get_provider_stats()
            }

        except Exception as e:
            logger.error(f"智能分析失败: {e}")
            return {
                "errors": [f"智能分析失败: {str(e)}"],
                "processing_stage": "error"
            }
    
    def _generate_report_node(self, state: AgentState) -> Dict[str, Any]:
        """生成报告节点"""
        try:
            logger.info("生成安全报告")

            # 基础报告数据
            basic_report = {
                "summary": {
                    "total_apis": state["total_apis_processed"],
                    "total_vulnerabilities": state["total_vulnerabilities_found"],
                    "analysis_time": datetime.now().isoformat(),
                    "start_time": state["start_time"].isoformat(),
                },
                "apis": [api.dict() for api in state["apis"]],
                "security_results": [result.dict() for result in state["security_results"]],
                "intelligent_analysis": state.get("intelligent_analysis_results", {}),
                "metadata": {
                    "agent_version": "1.0.0",
                    "input_type": state["input_type"],
                    "processing_stages": state["processing_stage"],
                    "llm_analysis_enabled": bool(state.get("intelligent_analysis_results")),
                    "llm_provider_stats": state.get("llm_provider_stats", {})
                }
            }

            # 尝试生成智能报告
            report_config = state["config"].get("reporting", {})
            if report_config.get("enabled", True) and state.get("intelligent_analysis_results"):
                try:
                    logger.info("生成智能增强报告")

                    # 使用报告生成器创建增强报告
                    enhanced_report_result = asyncio.run(
                        self.report_generator.generate_comprehensive_report(
                            state.get("intelligent_analysis_results", {}),
                            state["apis"],
                            state["security_results"],
                            format_type=report_config.get("format", "html"),
                            custom_config=report_config
                        )
                    )

                    basic_report["enhanced_report"] = enhanced_report_result

                except Exception as e:
                    logger.error(f"生成智能增强报告失败: {e}")
                    basic_report["enhanced_report_error"] = str(e)

            logger.info("安全报告生成完成")

            return {
                "final_report": basic_report,
                "processing_stage": "completed",
                "end_time": datetime.now()
            }

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return {
                "errors": [f"生成报告失败: {str(e)}"],
                "processing_stage": "error"
            }
    
    def _perform_basic_security_check(self, api: APIInfo) -> Optional[SecurityCheckResult]:
        """执行基础安全检查（简化版本）"""
        try:
            # 这里是一个简化的示例，实际实现会更复杂
            from ..agent.state import VulnerabilityType, RiskLevel
            
            # 检查是否包含敏感路径
            sensitive_paths = ['/admin', '/api/user', '/api/password']
            
            for sensitive_path in sensitive_paths:
                if sensitive_path in api.path.lower():
                    return SecurityCheckResult(
                        check_type=VulnerabilityType.FIELD_EXPOSURE,
                        risk_level=RiskLevel.MEDIUM,
                        is_vulnerable=True,
                        title="潜在的敏感路径暴露",
                        description=f"API路径 {api.path} 可能包含敏感信息",
                        technical_details={"path": api.path, "url": api.url},
                        evidence=[f"敏感路径: {sensitive_path}"],
                        recommendation="检查API路径是否需要额外的访问控制",
                        api_info=api
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"执行安全检查失败: {e}")
            return None
    
    def _load_all_configs(self) -> Dict[str, Any]:
        """加载所有配置"""
        try:
            return self.config_loader.load_all_configs()
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {}
