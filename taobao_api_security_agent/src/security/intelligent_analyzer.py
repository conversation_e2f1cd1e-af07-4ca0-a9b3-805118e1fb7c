"""
智能安全分析器

使用LLM进行深度安全分析和推理。
"""

import json
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel
from ..integrations.llm_provider import LLMProvider, LLMResponse
from ..utils.logger import get_logger

logger = get_logger(__name__)


class IntelligentSecurityAnalyzer:
    """智能安全分析器"""
    
    def __init__(
        self,
        llm_provider: LLMProvider,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化智能安全分析器
        
        Args:
            llm_provider: LLM提供商
            config: 配置信息
        """
        self.llm_provider = llm_provider
        self.config = config or {}
        
        # 分析配置
        self.analysis_enabled = self.config.get('enabled', True)
        self.max_context_length = self.config.get('max_context_length', 8000)
        self.analysis_temperature = self.config.get('temperature', 0.1)
        self.batch_size = self.config.get('batch_size', 5)
        
        # 提示词模板
        self.prompts = self._load_prompts()
    
    async def analyze_security_results(
        self,
        security_results: List[SecurityCheckResult],
        api_info: APIInfo,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析安全检测结果
        
        Args:
            security_results: 安全检测结果列表
            api_info: API信息
            context: 额外上下文信息
            
        Returns:
            智能分析结果
        """
        if not self.analysis_enabled or not security_results:
            return {}
        
        try:
            logger.info(f"开始智能分析 {len(security_results)} 个安全问题")
            
            # 构建分析上下文
            analysis_context = self._build_analysis_context(
                security_results, api_info, context
            )
            
            # 执行不同类型的分析
            analysis_results = {}
            
            # 1. 风险评估分析
            risk_analysis = await self._analyze_risk_assessment(analysis_context)
            analysis_results['risk_assessment'] = risk_analysis
            
            # 2. 漏洞关联分析
            correlation_analysis = await self._analyze_vulnerability_correlation(analysis_context)
            analysis_results['vulnerability_correlation'] = correlation_analysis
            
            # 3. 修复建议生成
            remediation_suggestions = await self._generate_remediation_suggestions(analysis_context)
            analysis_results['remediation_suggestions'] = remediation_suggestions
            
            # 4. 业务影响分析
            business_impact = await self._analyze_business_impact(analysis_context)
            analysis_results['business_impact'] = business_impact
            
            # 5. 攻击场景分析
            attack_scenarios = await self._analyze_attack_scenarios(analysis_context)
            analysis_results['attack_scenarios'] = attack_scenarios
            
            logger.info("智能分析完成")
            
            return {
                'analysis_results': analysis_results,
                'analysis_timestamp': datetime.now().isoformat(),
                'analyzed_issues_count': len(security_results),
                'llm_provider_stats': self.llm_provider.get_provider_stats()
            }
            
        except Exception as e:
            logger.error(f"智能分析失败: {e}")
            return {'error': str(e)}
    
    async def analyze_api_behavior(
        self,
        api_info: APIInfo,
        request_response_pairs: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析API行为模式
        
        Args:
            api_info: API信息
            request_response_pairs: 请求响应对列表
            context: 额外上下文
            
        Returns:
            行为分析结果
        """
        try:
            logger.info(f"开始分析API行为: {api_info.url}")
            
            # 构建行为分析上下文
            behavior_context = self._build_behavior_context(
                api_info, request_response_pairs, context
            )
            
            # 执行行为分析
            behavior_analysis = await self._analyze_behavior_patterns(behavior_context)
            
            return {
                'behavior_analysis': behavior_analysis,
                'analysis_timestamp': datetime.now().isoformat(),
                'analyzed_requests_count': len(request_response_pairs)
            }
            
        except Exception as e:
            logger.error(f"API行为分析失败: {e}")
            return {'error': str(e)}
    
    async def generate_security_summary(
        self,
        all_results: List[SecurityCheckResult],
        api_list: List[APIInfo],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成安全总结报告
        
        Args:
            all_results: 所有安全检测结果
            api_list: API列表
            context: 额外上下文
            
        Returns:
            安全总结
        """
        try:
            logger.info("生成智能安全总结")
            
            # 构建总结上下文
            summary_context = self._build_summary_context(
                all_results, api_list, context
            )
            
            # 生成总结
            security_summary = await self._generate_comprehensive_summary(summary_context)
            
            return {
                'security_summary': security_summary,
                'summary_timestamp': datetime.now().isoformat(),
                'total_apis_analyzed': len(api_list),
                'total_issues_found': len(all_results)
            }
            
        except Exception as e:
            logger.error(f"生成安全总结失败: {e}")
            return {'error': str(e)}
    
    async def _analyze_risk_assessment(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """风险评估分析"""
        try:
            system_prompt = self.prompts['risk_assessment']['system']
            user_prompt = self.prompts['risk_assessment']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            # 尝试解析JSON响应
            try:
                risk_data = json.loads(response.content)
            except json.JSONDecodeError:
                risk_data = {'analysis': response.content}
            
            return {
                'risk_analysis': risk_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"风险评估分析失败: {e}")
            return {'error': str(e)}
    
    async def _analyze_vulnerability_correlation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """漏洞关联分析"""
        try:
            system_prompt = self.prompts['vulnerability_correlation']['system']
            user_prompt = self.prompts['vulnerability_correlation']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                correlation_data = json.loads(response.content)
            except json.JSONDecodeError:
                correlation_data = {'analysis': response.content}
            
            return {
                'correlation_analysis': correlation_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"漏洞关联分析失败: {e}")
            return {'error': str(e)}
    
    async def _generate_remediation_suggestions(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成修复建议"""
        try:
            system_prompt = self.prompts['remediation_suggestions']['system']
            user_prompt = self.prompts['remediation_suggestions']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                remediation_data = json.loads(response.content)
            except json.JSONDecodeError:
                remediation_data = {'suggestions': response.content}
            
            return {
                'remediation_suggestions': remediation_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"生成修复建议失败: {e}")
            return {'error': str(e)}
    
    async def _analyze_business_impact(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """业务影响分析"""
        try:
            system_prompt = self.prompts['business_impact']['system']
            user_prompt = self.prompts['business_impact']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                impact_data = json.loads(response.content)
            except json.JSONDecodeError:
                impact_data = {'analysis': response.content}
            
            return {
                'business_impact': impact_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"业务影响分析失败: {e}")
            return {'error': str(e)}
    
    async def _analyze_attack_scenarios(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """攻击场景分析"""
        try:
            system_prompt = self.prompts['attack_scenarios']['system']
            user_prompt = self.prompts['attack_scenarios']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                scenario_data = json.loads(response.content)
            except json.JSONDecodeError:
                scenario_data = {'scenarios': response.content}
            
            return {
                'attack_scenarios': scenario_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"攻击场景分析失败: {e}")
            return {'error': str(e)}
    
    async def _analyze_behavior_patterns(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析行为模式"""
        try:
            system_prompt = self.prompts['behavior_analysis']['system']
            user_prompt = self.prompts['behavior_analysis']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                behavior_data = json.loads(response.content)
            except json.JSONDecodeError:
                behavior_data = {'analysis': response.content}
            
            return {
                'behavior_patterns': behavior_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"行为模式分析失败: {e}")
            return {'error': str(e)}
    
    async def _generate_comprehensive_summary(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合总结"""
        try:
            system_prompt = self.prompts['comprehensive_summary']['system']
            user_prompt = self.prompts['comprehensive_summary']['user'].format(**context)
            
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm_provider.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                temperature=self.analysis_temperature
            )
            
            try:
                summary_data = json.loads(response.content)
            except json.JSONDecodeError:
                summary_data = {'summary': response.content}
            
            return {
                'comprehensive_summary': summary_data,
                'llm_response': response.content,
                'provider': response.provider,
                'response_time': response.response_time
            }
            
        except Exception as e:
            logger.error(f"生成综合总结失败: {e}")
            return {'error': str(e)}
    
    def _build_analysis_context(
        self,
        security_results: List[SecurityCheckResult],
        api_info: APIInfo,
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """构建分析上下文"""
        # 整理安全问题
        issues_by_type = {}
        for result in security_results:
            issue_type = result.check_type
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            
            issues_by_type[issue_type].append({
                'title': result.title,
                'description': result.description,
                'risk_level': result.risk_level,
                'evidence': result.evidence,
                'technical_details': result.technical_details
            })
        
        # 构建上下文
        analysis_context = {
            'api_url': api_info.url,
            'api_method': api_info.method,
            'api_domain': api_info.domain,
            'api_path': api_info.path,
            'total_issues': len(security_results),
            'issues_by_type': json.dumps(issues_by_type, indent=2, ensure_ascii=False),
            'context_info': json.dumps(context or {}, indent=2, ensure_ascii=False)
        }
        
        return analysis_context
    
    def _build_behavior_context(
        self,
        api_info: APIInfo,
        request_response_pairs: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """构建行为分析上下文"""
        # 提取关键信息
        response_patterns = []
        for pair in request_response_pairs[:10]:  # 限制数量
            response_patterns.append({
                'status_code': pair.get('response', {}).get('status_code'),
                'response_size': len(pair.get('response', {}).get('body', '')),
                'response_time': pair.get('response_time', 0)
            })
        
        behavior_context = {
            'api_url': api_info.url,
            'api_method': api_info.method,
            'total_requests': len(request_response_pairs),
            'response_patterns': json.dumps(response_patterns, indent=2),
            'context_info': json.dumps(context or {}, indent=2, ensure_ascii=False)
        }
        
        return behavior_context
    
    def _build_summary_context(
        self,
        all_results: List[SecurityCheckResult],
        api_list: List[APIInfo],
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """构建总结上下文"""
        # 统计信息
        risk_distribution = {}
        vulnerability_types = {}
        
        for result in all_results:
            # 风险等级分布
            risk_level = result.risk_level
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
            
            # 漏洞类型分布
            vuln_type = result.check_type
            vulnerability_types[vuln_type] = vulnerability_types.get(vuln_type, 0) + 1
        
        # API统计
        api_domains = {}
        for api in api_list:
            domain = api.domain
            api_domains[domain] = api_domains.get(domain, 0) + 1
        
        summary_context = {
            'total_apis': len(api_list),
            'total_issues': len(all_results),
            'risk_distribution': json.dumps(risk_distribution, indent=2),
            'vulnerability_types': json.dumps(vulnerability_types, indent=2),
            'api_domains': json.dumps(api_domains, indent=2),
            'context_info': json.dumps(context or {}, indent=2, ensure_ascii=False)
        }
        
        return summary_context
    
    def _load_prompts(self) -> Dict[str, Dict[str, str]]:
        """加载提示词模板"""
        return {
            'risk_assessment': {
                'system': """你是一个专业的API安全分析专家。请基于提供的安全检测结果，进行深入的风险评估分析。

分析要求：
1. 评估每个安全问题的实际风险等级
2. 考虑业务场景和攻击可行性
3. 提供风险优先级排序
4. 给出风险量化评分

请以JSON格式返回分析结果。""",
                'user': """请分析以下API安全检测结果：

API信息：
- URL: {api_url}
- 方法: {api_method}
- 域名: {api_domain}
- 路径: {api_path}

发现的安全问题（共{total_issues}个）：
{issues_by_type}

额外上下文：
{context_info}

请提供详细的风险评估分析。"""
            },
            
            'vulnerability_correlation': {
                'system': """你是一个API安全专家。请分析不同安全漏洞之间的关联性，识别可能的攻击链和复合攻击场景。

分析要点：
1. 漏洞之间的依赖关系
2. 可能的攻击链组合
3. 复合攻击的可行性
4. 关联漏洞的修复优先级

请以JSON格式返回分析结果。""",
                'user': """请分析以下安全问题的关联性：

API: {api_url}
安全问题：
{issues_by_type}

请识别漏洞间的关联关系和潜在的攻击链。"""
            },
            
            'remediation_suggestions': {
                'system': """你是一个API安全修复专家。请为发现的安全问题提供具体、可操作的修复建议。

建议要求：
1. 技术实现细节
2. 代码示例（如适用）
3. 配置修改建议
4. 最佳实践推荐
5. 修复优先级

请以JSON格式返回修复建议。""",
                'user': """请为以下安全问题提供修复建议：

API: {api_url}
安全问题：
{issues_by_type}

请提供详细的修复方案和实施建议。"""
            },
            
            'business_impact': {
                'system': """你是一个业务安全分析专家。请评估API安全问题对业务的潜在影响。

评估维度：
1. 数据泄露风险
2. 业务中断可能性
3. 合规性影响
4. 声誉损失
5. 财务损失

请以JSON格式返回影响分析。""",
                'user': """请评估以下安全问题的业务影响：

API: {api_url}（淘宝相关API）
安全问题：
{issues_by_type}

请分析对电商业务的具体影响。"""
            },
            
            'attack_scenarios': {
                'system': """你是一个渗透测试专家。请基于发现的安全问题，构建具体的攻击场景。

场景要求：
1. 攻击步骤详述
2. 所需条件和工具
3. 预期攻击效果
4. 检测难度评估

请以JSON格式返回攻击场景。""",
                'user': """请构建以下安全问题的攻击场景：

API: {api_url}
安全问题：
{issues_by_type}

请提供详细的攻击场景和利用方法。"""
            },
            
            'behavior_analysis': {
                'system': """你是一个API行为分析专家。请分析API的行为模式，识别异常和潜在的安全风险。

分析要点：
1. 响应模式分析
2. 性能特征
3. 异常行为识别
4. 安全风险评估

请以JSON格式返回分析结果。""",
                'user': """请分析以下API的行为模式：

API: {api_url}
请求总数: {total_requests}
响应模式：
{response_patterns}

请识别异常行为和潜在风险。"""
            },
            
            'comprehensive_summary': {
                'system': """你是一个高级安全顾问。请基于所有安全检测结果，生成综合性的安全评估报告。

报告要求：
1. 执行摘要
2. 关键发现
3. 风险评估
4. 修复路线图
5. 长期建议

请以JSON格式返回综合报告。""",
                'user': """请生成综合安全评估报告：

分析范围：
- 总API数量: {total_apis}
- 发现问题数: {total_issues}
- 风险分布: {risk_distribution}
- 漏洞类型: {vulnerability_types}
- 涉及域名: {api_domains}

请提供全面的安全评估和建议。"""
            }
        }
