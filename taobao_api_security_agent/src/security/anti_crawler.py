"""
反爬策略检测器

检测API的反爬虫防护措施是否充分。
"""

import asyncio
import time
import random
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel, SessionInfo
from .request_replayer import RequestReplayer
from ..utils.logger import get_logger

logger = get_logger(__name__)


class AntiCrawlerDetector:
    """反爬策略检测器"""
    
    def __init__(
        self,
        request_replayer: RequestReplayer,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化反爬策略检测器
        
        Args:
            request_replayer: 请求重放器
            config: 配置信息
        """
        self.request_replayer = request_replayer
        self.config = config or {}
        
        # 检测配置
        self.rate_limit_test_count = self.config.get('rate_limit_test_count', 20)
        self.rate_limit_interval = self.config.get('rate_limit_interval', 1)  # 秒
        self.user_agent_test_enabled = self.config.get('user_agent_test_enabled', True)
        self.captcha_detection_enabled = self.config.get('captcha_detection_enabled', True)
        self.behavior_analysis_enabled = self.config.get('behavior_analysis_enabled', True)
        
        # 测试用的User-Agent列表
        self.test_user_agents = self._load_test_user_agents()
        
        # 可疑行为模式
        self.suspicious_patterns = self._load_suspicious_patterns()
    
    async def detect_anti_crawler_measures(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo] = None
    ) -> List[SecurityCheckResult]:
        """
        检测反爬策略
        
        Args:
            api_info: API信息
            session: 会话信息
            
        Returns:
            安全检查结果列表
        """
        results = []
        
        try:
            logger.info(f"开始检测反爬策略: {api_info.url}")
            
            # 1. 频率限制检测
            rate_limit_results = await self._test_rate_limiting(api_info, session)
            results.extend(rate_limit_results)
            
            # 2. User-Agent检测
            if self.user_agent_test_enabled:
                ua_results = await self._test_user_agent_filtering(api_info, session)
                results.extend(ua_results)
            
            # 3. 验证码检测
            if self.captcha_detection_enabled:
                captcha_results = await self._test_captcha_protection(api_info, session)
                results.extend(captcha_results)
            
            # 4. IP封禁检测
            ip_blocking_results = await self._test_ip_blocking(api_info, session)
            results.extend(ip_blocking_results)
            
            # 5. 行为分析检测
            if self.behavior_analysis_enabled:
                behavior_results = await self._test_behavior_analysis(api_info, session)
                results.extend(behavior_results)
            
            logger.info(f"反爬策略检测完成，发现 {len(results)} 个问题")
            
        except Exception as e:
            logger.error(f"反爬策略检测失败: {e}")
        
        return results
    
    async def _test_rate_limiting(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试频率限制"""
        results = []
        
        try:
            logger.info("测试频率限制")
            
            # 记录请求时间和响应
            request_times = []
            responses = []
            
            # 快速连续发送请求
            for i in range(self.rate_limit_test_count):
                start_time = time.time()
                
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session
                )
                
                end_time = time.time()
                request_times.append(end_time - start_time)
                
                if success:
                    responses.append(response_data['response'])
                else:
                    responses.append({'status_code': 0, 'body': '', 'error': True})
                
                # 短暂延迟
                await asyncio.sleep(self.rate_limit_interval)
            
            # 分析频率限制效果
            rate_limit_analysis = self._analyze_rate_limiting(responses, api_info)
            results.extend(rate_limit_analysis)
            
        except Exception as e:
            logger.error(f"频率限制测试失败: {e}")
        
        return results
    
    async def _test_user_agent_filtering(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试User-Agent过滤"""
        results = []
        
        try:
            logger.info("测试User-Agent过滤")
            
            # 测试不同的User-Agent
            ua_responses = {}
            
            for ua_name, ua_string in self.test_user_agents.items():
                # 修改请求头
                modified_headers = (session.headers.copy() if session else {})
                modified_headers['User-Agent'] = ua_string
                
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session, modified_headers=modified_headers
                )
                
                if success:
                    ua_responses[ua_name] = response_data['response']
                
                # 短暂延迟
                await asyncio.sleep(1)
            
            # 分析User-Agent过滤效果
            ua_analysis = self._analyze_user_agent_filtering(ua_responses, api_info)
            results.extend(ua_analysis)
            
        except Exception as e:
            logger.error(f"User-Agent过滤测试失败: {e}")
        
        return results
    
    async def _test_captcha_protection(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试验证码保护"""
        results = []
        
        try:
            logger.info("测试验证码保护")
            
            # 发送多个请求，检查是否出现验证码
            captcha_detected = False
            
            for i in range(10):  # 发送10个请求
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session
                )
                
                if success:
                    response_body = response_data['response'].get('body', '')
                    
                    # 检查是否包含验证码相关内容
                    if self._contains_captcha_indicators(response_body):
                        captcha_detected = True
                        break
                
                await asyncio.sleep(0.5)
            
            # 分析验证码保护
            if not captcha_detected:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.ANTI_CRAWLER_BYPASS,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="缺少验证码保护",
                    description="API在多次快速请求后未触发验证码保护",
                    technical_details={
                        'test_requests': 10,
                        'captcha_detected': False
                    },
                    evidence=["连续10次请求未触发验证码"],
                    recommendation="考虑在检测到异常访问模式时启用验证码保护",
                    api_info=api_info
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"验证码保护测试失败: {e}")
        
        return results
    
    async def _test_ip_blocking(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试IP封禁"""
        results = []
        
        try:
            logger.info("测试IP封禁")
            
            # 模拟大量请求
            blocked = False
            consecutive_failures = 0
            
            for i in range(50):  # 发送50个请求
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session
                )
                
                if not success or response_data.get('response', {}).get('status_code', 0) >= 400:
                    consecutive_failures += 1
                    if consecutive_failures >= 5:  # 连续5次失败认为可能被封禁
                        blocked = True
                        break
                else:
                    consecutive_failures = 0
                
                # 快速请求
                await asyncio.sleep(0.1)
            
            # 分析IP封禁效果
            if not blocked:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.ANTI_CRAWLER_BYPASS,
                    risk_level=RiskLevel.LOW,
                    is_vulnerable=True,
                    title="缺少IP封禁机制",
                    description="API在大量快速请求后未实施IP封禁",
                    technical_details={
                        'test_requests': 50,
                        'ip_blocked': False,
                        'consecutive_failures': consecutive_failures
                    },
                    evidence=["50次快速请求未触发IP封禁"],
                    recommendation="考虑实施基于IP的访问频率限制和封禁机制",
                    api_info=api_info
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"IP封禁测试失败: {e}")
        
        return results
    
    async def _test_behavior_analysis(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试行为分析"""
        results = []
        
        try:
            logger.info("测试行为分析")
            
            # 模拟可疑行为模式
            suspicious_behaviors = [
                "rapid_requests",      # 快速请求
                "pattern_requests",    # 规律性请求
                "parameter_scanning",  # 参数扫描
                "error_probing"        # 错误探测
            ]
            
            behavior_results = {}
            
            for behavior in suspicious_behaviors:
                try:
                    behavior_detected = await self._simulate_suspicious_behavior(
                        behavior, api_info, session
                    )
                    behavior_results[behavior] = behavior_detected
                except Exception as e:
                    logger.warning(f"行为测试 {behavior} 失败: {e}")
                    behavior_results[behavior] = False
            
            # 分析行为检测效果
            undetected_behaviors = [
                behavior for behavior, detected in behavior_results.items()
                if not detected
            ]
            
            if undetected_behaviors:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.ANTI_CRAWLER_BYPASS,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="缺少行为分析保护",
                    description=f"以下可疑行为未被检测: {', '.join(undetected_behaviors)}",
                    technical_details={
                        'tested_behaviors': list(behavior_results.keys()),
                        'undetected_behaviors': undetected_behaviors,
                        'detection_results': behavior_results
                    },
                    evidence=[f"未检测到的行为: {', '.join(undetected_behaviors)}"],
                    recommendation="实施智能行为分析系统检测异常访问模式",
                    api_info=api_info
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"行为分析测试失败: {e}")
        
        return results
    
    async def _simulate_suspicious_behavior(
        self,
        behavior_type: str,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> bool:
        """模拟可疑行为"""
        try:
            if behavior_type == "rapid_requests":
                # 快速连续请求
                for i in range(20):
                    await self.request_replayer.replay_request(api_info, session)
                    await asyncio.sleep(0.05)  # 50ms间隔
            
            elif behavior_type == "pattern_requests":
                # 规律性请求
                for i in range(10):
                    await self.request_replayer.replay_request(api_info, session)
                    await asyncio.sleep(2)  # 固定2秒间隔
            
            elif behavior_type == "parameter_scanning":
                # 参数扫描
                original_params = api_info.request_params.copy()
                scan_params = ['admin', 'test', 'debug', 'internal', 'system']
                
                for param in scan_params:
                    modified_params = original_params.copy()
                    modified_params[param] = '1'
                    await self.request_replayer.replay_request(
                        api_info, session, modified_params=modified_params
                    )
                    await asyncio.sleep(0.5)
            
            elif behavior_type == "error_probing":
                # 错误探测
                error_payloads = ["'", '"', '<', '>', '&', '%', '\\']
                original_params = api_info.request_params.copy()
                
                for payload in error_payloads:
                    for param_name in original_params.keys():
                        modified_params = original_params.copy()
                        modified_params[param_name] = payload
                        await self.request_replayer.replay_request(
                            api_info, session, modified_params=modified_params
                        )
                        await asyncio.sleep(0.3)
            
            # 检查是否被检测到（简化的检测逻辑）
            # 实际实现中需要更复杂的检测机制
            return False  # 假设未被检测到
            
        except Exception as e:
            logger.error(f"模拟可疑行为 {behavior_type} 失败: {e}")
            return False
    
    def _analyze_rate_limiting(
        self,
        responses: List[Dict[str, Any]],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """分析频率限制效果"""
        results = []
        
        try:
            # 统计响应状态
            status_codes = [resp.get('status_code', 0) for resp in responses]
            success_count = sum(1 for code in status_codes if 200 <= code < 300)
            rate_limited_count = sum(1 for code in status_codes if code == 429)
            
            # 计算成功率
            success_rate = success_count / len(responses) if responses else 0
            
            # 检查是否缺少频率限制
            if success_rate > 0.8 and rate_limited_count == 0:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.ANTI_CRAWLER_BYPASS,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="缺少频率限制保护",
                    description=f"在{len(responses)}次快速请求中，成功率为{success_rate:.1%}，未触发频率限制",
                    technical_details={
                        'total_requests': len(responses),
                        'success_count': success_count,
                        'success_rate': success_rate,
                        'rate_limited_count': rate_limited_count,
                        'status_codes': status_codes
                    },
                    evidence=[f"成功率: {success_rate:.1%}", f"429状态码次数: {rate_limited_count}"],
                    recommendation="实施适当的API频率限制机制",
                    api_info=api_info
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"分析频率限制失败: {e}")
        
        return results
    
    def _analyze_user_agent_filtering(
        self,
        ua_responses: Dict[str, Dict[str, Any]],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """分析User-Agent过滤效果"""
        results = []
        
        try:
            # 检查不同User-Agent的响应
            successful_uas = []
            blocked_uas = []
            
            for ua_name, response in ua_responses.items():
                status_code = response.get('status_code', 0)
                if 200 <= status_code < 300:
                    successful_uas.append(ua_name)
                elif status_code in [403, 406, 429]:
                    blocked_uas.append(ua_name)
            
            # 检查是否对可疑User-Agent进行了过滤
            suspicious_uas = ['bot', 'crawler', 'spider', 'scraper']
            unblocked_suspicious = [
                ua for ua in successful_uas 
                if any(suspicious in ua.lower() for suspicious in suspicious_uas)
            ]
            
            if unblocked_suspicious:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.ANTI_CRAWLER_BYPASS,
                    risk_level=RiskLevel.LOW,
                    is_vulnerable=True,
                    title="User-Agent过滤不充分",
                    description=f"可疑的User-Agent未被阻止: {', '.join(unblocked_suspicious)}",
                    technical_details={
                        'successful_uas': successful_uas,
                        'blocked_uas': blocked_uas,
                        'unblocked_suspicious': unblocked_suspicious
                    },
                    evidence=[f"未阻止的可疑UA: {', '.join(unblocked_suspicious)}"],
                    recommendation="加强User-Agent过滤，阻止明显的爬虫User-Agent",
                    api_info=api_info
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"分析User-Agent过滤失败: {e}")
        
        return results
    
    def _contains_captcha_indicators(self, response_body: str) -> bool:
        """检查响应是否包含验证码指示器"""
        captcha_indicators = [
            'captcha', 'verify', 'verification', 'challenge',
            '验证码', '人机验证', '滑动验证', '点击验证'
        ]
        
        response_lower = response_body.lower()
        return any(indicator in response_lower for indicator in captcha_indicators)
    
    def _load_test_user_agents(self) -> Dict[str, str]:
        """加载测试用的User-Agent"""
        return {
            'normal_browser': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'mobile_browser': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'bot': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
            'crawler': 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)',
            'spider': 'Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)',
            'scraper': 'Python-requests/2.25.1',
            'curl': 'curl/7.68.0',
            'wget': 'Wget/1.20.3 (linux-gnu)',
            'empty': '',
            'suspicious': 'AutomatedTool/1.0'
        }
    
    def _load_suspicious_patterns(self) -> List[str]:
        """加载可疑行为模式"""
        return [
            'rapid_sequential_requests',
            'identical_request_intervals',
            'parameter_enumeration',
            'error_code_probing',
            'response_time_analysis',
            'header_manipulation',
            'cookie_manipulation'
        ]
