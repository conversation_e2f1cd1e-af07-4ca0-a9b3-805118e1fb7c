"""
请求重放器

负责重放HTTP请求，支持参数修改、签名重新生成等功能。
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlencode, parse_qs, urlparse, urlunparse
import requests
import aiohttp
import logging
from datetime import datetime

from ..agent.state import APIInfo, SessionInfo, SignatureInfo
from ..integrations.signature_service import SignatureService
from ..integrations.session_manager import SessionManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RequestReplayer:
    """请求重放器"""
    
    def __init__(
        self,
        signature_service: Optional[SignatureService] = None,
        session_manager: Optional[SessionManager] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化请求重放器
        
        Args:
            signature_service: 签名服务
            session_manager: 会话管理器
            config: 配置信息
        """
        self.signature_service = signature_service
        self.session_manager = session_manager
        self.config = config or {}
        
        # 请求配置
        self.timeout = self.config.get('request_timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1)
        self.max_concurrent = self.config.get('max_concurrent_requests', 10)
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0
        }
    
    async def replay_request(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo] = None,
        modified_params: Optional[Dict[str, Any]] = None,
        modified_headers: Optional[Dict[str, str]] = None,
        modified_body: Optional[str] = None,
        regenerate_signature: bool = True
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        重放单个请求
        
        Args:
            api_info: API信息
            session: 会话信息
            modified_params: 修改的参数
            modified_headers: 修改的头部
            modified_body: 修改的请求体
            regenerate_signature: 是否重新生成签名
            
        Returns:
            (是否成功, 响应信息)
        """
        try:
            start_time = time.time()
            
            # 构建请求
            request_data = await self._build_request(
                api_info, session, modified_params, modified_headers, 
                modified_body, regenerate_signature
            )
            
            # 发送请求
            response_data = await self._send_request(request_data)
            
            # 更新统计
            response_time = time.time() - start_time
            self._update_stats(True, response_time)
            
            # 更新会话统计
            if session and self.session_manager:
                self.session_manager.update_session_stats(session, True, response_time)
            
            logger.debug(f"请求重放成功: {api_info.url}")
            
            return True, {
                'request': request_data,
                'response': response_data,
                'response_time': response_time,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"请求重放失败: {api_info.url}, 错误: {e}")
            
            self._update_stats(False, 0)
            
            if session and self.session_manager:
                self.session_manager.update_session_stats(session, False)
            
            return False, {
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    async def replay_multiple_requests(
        self,
        requests_data: List[Tuple[APIInfo, Dict[str, Any]]],
        session: Optional[SessionInfo] = None
    ) -> List[Tuple[bool, Dict[str, Any]]]:
        """
        批量重放请求
        
        Args:
            requests_data: 请求数据列表，每个元素为(api_info, modifications)
            session: 会话信息
            
        Returns:
            重放结果列表
        """
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def replay_single(api_info, modifications):
            async with semaphore:
                return await self.replay_request(
                    api_info, session, 
                    modifications.get('params'),
                    modifications.get('headers'),
                    modifications.get('body'),
                    modifications.get('regenerate_signature', True)
                )
        
        tasks = [
            replay_single(api_info, modifications) 
            for api_info, modifications in requests_data
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append((False, {'error': str(result)}))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def replay_request_sync(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo] = None,
        modified_params: Optional[Dict[str, Any]] = None,
        modified_headers: Optional[Dict[str, str]] = None,
        modified_body: Optional[str] = None,
        regenerate_signature: bool = True
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        同步版本的请求重放
        
        Args:
            api_info: API信息
            session: 会话信息
            modified_params: 修改的参数
            modified_headers: 修改的头部
            modified_body: 修改的请求体
            regenerate_signature: 是否重新生成签名
            
        Returns:
            (是否成功, 响应信息)
        """
        try:
            start_time = time.time()
            
            # 构建请求
            request_data = self._build_request_sync(
                api_info, session, modified_params, modified_headers,
                modified_body, regenerate_signature
            )
            
            # 发送请求
            response_data = self._send_request_sync(request_data)
            
            # 更新统计
            response_time = time.time() - start_time
            self._update_stats(True, response_time)
            
            logger.debug(f"同步请求重放成功: {api_info.url}")
            
            return True, {
                'request': request_data,
                'response': response_data,
                'response_time': response_time,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"同步请求重放失败: {api_info.url}, 错误: {e}")
            self._update_stats(False, 0)
            
            return False, {
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    async def _build_request(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo],
        modified_params: Optional[Dict[str, Any]],
        modified_headers: Optional[Dict[str, str]],
        modified_body: Optional[str],
        regenerate_signature: bool
    ) -> Dict[str, Any]:
        """构建请求数据"""
        # 基础请求信息
        url = api_info.url
        method = api_info.method
        
        # 合并参数
        params = api_info.request_params.copy()
        if modified_params:
            params.update(modified_params)
        
        # 合并头部
        headers = api_info.request_headers.copy()
        if session and self.session_manager:
            session_headers = self.session_manager.get_session_headers(session)
            headers.update(session_headers)
        if modified_headers:
            headers.update(modified_headers)
        
        # 请求体
        body = modified_body if modified_body is not None else api_info.request_body
        
        # 重新生成签名
        if regenerate_signature and self.signature_service:
            try:
                signature_info = self.signature_service.generate_signature(
                    url, method, params, headers, body
                )
                # 将签名添加到参数中
                params['sign'] = signature_info.signature
                
                # 更新时间戳等系统参数
                params.update(signature_info.parameters)
                
            except Exception as e:
                logger.warning(f"生成签名失败: {e}")
        
        # 构建最终URL
        if params and method.upper() == 'GET':
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            query_params.update({k: [str(v)] for k, v in params.items()})
            
            new_query = urlencode(query_params, doseq=True)
            final_url = urlunparse((
                parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                parsed_url.params, new_query, parsed_url.fragment
            ))
        else:
            final_url = url
        
        return {
            'url': final_url,
            'method': method,
            'headers': headers,
            'params': params if method.upper() != 'GET' else None,
            'data': body,
            'timeout': self.timeout
        }
    
    def _build_request_sync(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo],
        modified_params: Optional[Dict[str, Any]],
        modified_headers: Optional[Dict[str, str]],
        modified_body: Optional[str],
        regenerate_signature: bool
    ) -> Dict[str, Any]:
        """同步版本的构建请求数据"""
        # 基础请求信息
        url = api_info.url
        method = api_info.method
        
        # 合并参数
        params = api_info.request_params.copy()
        if modified_params:
            params.update(modified_params)
        
        # 合并头部
        headers = api_info.request_headers.copy()
        if session and self.session_manager:
            session_headers = self.session_manager.get_session_headers(session)
            headers.update(session_headers)
        if modified_headers:
            headers.update(modified_headers)
        
        # 请求体
        body = modified_body if modified_body is not None else api_info.request_body
        
        # 重新生成签名
        if regenerate_signature and self.signature_service:
            try:
                signature_info = self.signature_service.generate_signature(
                    url, method, params, headers, body
                )
                params['sign'] = signature_info.signature
                params.update(signature_info.parameters)
            except Exception as e:
                logger.warning(f"生成签名失败: {e}")
        
        return {
            'url': url,
            'method': method,
            'headers': headers,
            'params': params,
            'data': body,
            'timeout': self.timeout
        }
    
    async def _send_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送异步请求"""
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method=request_data['method'],
                url=request_data['url'],
                headers=request_data['headers'],
                params=request_data.get('params'),
                data=request_data.get('data'),
                timeout=aiohttp.ClientTimeout(total=request_data['timeout'])
            ) as response:
                
                response_headers = dict(response.headers)
                response_body = await response.text()
                
                return {
                    'status_code': response.status,
                    'headers': response_headers,
                    'body': response_body,
                    'url': str(response.url),
                    'content_type': response_headers.get('content-type', ''),
                    'content_length': len(response_body)
                }
    
    def _send_request_sync(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送同步请求"""
        response = requests.request(
            method=request_data['method'],
            url=request_data['url'],
            headers=request_data['headers'],
            params=request_data.get('params'),
            data=request_data.get('data'),
            timeout=request_data['timeout']
        )
        
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'body': response.text,
            'url': response.url,
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.text)
        }
    
    def _update_stats(self, success: bool, response_time: float):
        """更新请求统计"""
        self.request_stats['total_requests'] += 1
        self.request_stats['total_response_time'] += response_time
        
        if success:
            self.request_stats['successful_requests'] += 1
        else:
            self.request_stats['failed_requests'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取请求统计信息"""
        stats = self.request_stats.copy()
        
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
            stats['average_response_time'] = stats['total_response_time'] / stats['total_requests']
        else:
            stats['success_rate'] = 0
            stats['average_response_time'] = 0
        
        return stats
