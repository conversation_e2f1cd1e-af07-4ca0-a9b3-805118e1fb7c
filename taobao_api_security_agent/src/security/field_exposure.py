"""
字段过度透出检测器

检测API响应中是否存在敏感字段过度透出的问题。
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel, SessionInfo
from .request_replayer import RequestReplayer
from .response_analyzer import ResponseAnalyzer
from .parameter_fuzzer import ParameterFuzzer
from ..utils.logger import get_logger

logger = get_logger(__name__)


class FieldExposureDetector:
    """字段过度透出检测器"""
    
    def __init__(
        self,
        request_replayer: RequestReplayer,
        response_analyzer: ResponseAnalyzer,
        parameter_fuzzer: ParameterFuzzer,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化字段过度透出检测器
        
        Args:
            request_replayer: 请求重放器
            response_analyzer: 响应分析器
            parameter_fuzzer: 参数Fuzz测试器
            config: 配置信息
        """
        self.request_replayer = request_replayer
        self.response_analyzer = response_analyzer
        self.parameter_fuzzer = parameter_fuzzer
        self.config = config or {}
        
        # 检测配置
        self.max_test_iterations = self.config.get('max_test_iterations', 20)
        self.test_different_users = self.config.get('test_different_users', True)
        self.test_parameter_manipulation = self.config.get('test_parameter_manipulation', True)
    
    async def detect_field_exposure(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """
        检测字段过度透出
        
        Args:
            api_info: API信息
            sessions: 可用的会话列表
            
        Returns:
            安全检查结果列表
        """
        results = []
        
        try:
            logger.info(f"开始检测字段过度透出: {api_info.url}")
            
            # 1. 基础响应分析
            baseline_results = await self._analyze_baseline_response(api_info, sessions)
            results.extend(baseline_results)
            
            # 2. 不同用户会话测试
            if self.test_different_users and len(sessions) > 1:
                session_results = await self._test_different_sessions(api_info, sessions)
                results.extend(session_results)
            
            # 3. 参数操作测试
            if self.test_parameter_manipulation:
                param_results = await self._test_parameter_manipulation(api_info, sessions[0] if sessions else None)
                results.extend(param_results)
            
            # 4. 特殊字符和边界值测试
            boundary_results = await self._test_boundary_values(api_info, sessions[0] if sessions else None)
            results.extend(boundary_results)
            
            logger.info(f"字段过度透出检测完成，发现 {len(results)} 个问题")
            
        except Exception as e:
            logger.error(f"字段过度透出检测失败: {e}")
        
        return results
    
    async def _analyze_baseline_response(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """分析基础响应"""
        results = []
        
        try:
            # 使用第一个可用会话进行基础测试
            session = sessions[0] if sessions else None
            
            # 重放原始请求
            success, response_data = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if success:
                # 分析响应中的敏感信息
                analysis_results = self.response_analyzer.analyze_response(
                    response_data['response'], api_info
                )
                results.extend(analysis_results)
                
                # 检查响应结构
                structure_results = self._analyze_response_structure(
                    response_data['response'], api_info
                )
                results.extend(structure_results)
            
        except Exception as e:
            logger.error(f"基础响应分析失败: {e}")
        
        return results
    
    async def _test_different_sessions(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试不同用户会话的响应差异"""
        results = []
        
        try:
            responses = {}
            
            # 收集不同会话的响应
            for session in sessions[:3]:  # 限制测试会话数量
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session
                )
                
                if success:
                    responses[session.user_id] = response_data['response']
            
            # 比较响应差异
            if len(responses) >= 2:
                comparison_results = self._compare_session_responses(
                    responses, api_info
                )
                results.extend(comparison_results)
        
        except Exception as e:
            logger.error(f"不同会话测试失败: {e}")
        
        return results
    
    async def _test_parameter_manipulation(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试参数操作"""
        results = []
        
        try:
            original_params = api_info.request_params.copy()
            
            # 生成参数操作测试用例
            test_cases = self._generate_parameter_test_cases(original_params)
            
            # 获取原始响应作为基准
            success, baseline_response = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if not success:
                return results
            
            # 执行参数操作测试
            for test_name, modified_params in test_cases:
                try:
                    success, test_response = await self.request_replayer.replay_request(
                        api_info, session, modified_params=modified_params
                    )
                    
                    if success:
                        # 比较响应差异
                        comparison_results = self._compare_parameter_responses(
                            baseline_response['response'],
                            test_response['response'],
                            test_name,
                            api_info
                        )
                        results.extend(comparison_results)
                
                except Exception as e:
                    logger.warning(f"参数测试 {test_name} 失败: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"参数操作测试失败: {e}")
        
        return results
    
    async def _test_boundary_values(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试边界值和特殊字符"""
        results = []
        
        try:
            original_params = api_info.request_params.copy()
            
            # 使用参数Fuzz测试器生成测试用例
            fuzz_generator = self.parameter_fuzzer.generate_fuzz_parameters(
                original_params,
                ['boundary_values', 'type_confusion', 'unicode', 'null_byte']
            )
            
            # 获取原始响应
            success, baseline_response = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if not success:
                return results
            
            # 执行边界值测试
            test_count = 0
            for fuzz_type, fuzz_params in fuzz_generator:
                if test_count >= self.max_test_iterations:
                    break
                
                try:
                    success, fuzz_response = await self.request_replayer.replay_request(
                        api_info, session, modified_params=fuzz_params
                    )
                    
                    if success:
                        # 分析Fuzz响应
                        fuzz_results = self.parameter_fuzzer.analyze_fuzz_response(
                            baseline_response['response'],
                            fuzz_response['response'],
                            fuzz_type,
                            api_info
                        )
                        results.extend(fuzz_results)
                    
                    test_count += 1
                
                except Exception as e:
                    logger.warning(f"边界值测试 {fuzz_type} 失败: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"边界值测试失败: {e}")
        
        return results
    
    def _analyze_response_structure(
        self,
        response_data: Dict[str, Any],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """分析响应结构"""
        results = []
        
        try:
            response_body = response_data.get('body', '')
            
            # 检查响应是否过于详细
            if len(response_body) > 10000:  # 响应过大
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.FIELD_EXPOSURE,
                    risk_level=RiskLevel.LOW,
                    is_vulnerable=True,
                    title="响应数据过于详细",
                    description=f"API响应包含大量数据 ({len(response_body)} 字符)",
                    technical_details={
                        'response_size': len(response_body),
                        'content_type': response_data.get('content_type', '')
                    },
                    evidence=[f"响应大小: {len(response_body)} 字符"],
                    recommendation="检查是否需要返回如此详细的数据，考虑分页或字段过滤",
                    api_info=api_info
                )
                results.append(result)
            
            # 检查是否包含调试信息
            debug_indicators = [
                'debug', 'trace', 'stack', 'error', 'exception',
                'sql', 'query', 'database', 'internal'
            ]
            
            response_lower = response_body.lower()
            found_debug_info = [indicator for indicator in debug_indicators if indicator in response_lower]
            
            if found_debug_info:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.FIELD_EXPOSURE,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="响应包含调试信息",
                    description=f"API响应可能包含调试信息: {', '.join(found_debug_info)}",
                    technical_details={
                        'debug_indicators': found_debug_info,
                        'response_snippet': response_body[:200] + '...' if len(response_body) > 200 else response_body
                    },
                    evidence=[f"发现调试关键词: {', '.join(found_debug_info)}"],
                    recommendation="移除生产环境中的调试信息",
                    api_info=api_info
                )
                results.append(result)
        
        except Exception as e:
            logger.error(f"分析响应结构失败: {e}")
        
        return results
    
    def _compare_session_responses(
        self,
        responses: Dict[str, Dict[str, Any]],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """比较不同会话的响应"""
        results = []
        
        try:
            response_bodies = {user_id: resp.get('body', '') for user_id, resp in responses.items()}
            
            # 检查响应是否完全相同（可能存在权限问题）
            unique_responses = set(response_bodies.values())
            
            if len(unique_responses) == 1:
                # 所有用户得到相同响应，可能存在权限控制问题
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.PRIVILEGE_ESCALATION,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="不同用户获得相同响应",
                    description="不同类型的用户获得了相同的API响应，可能存在权限控制问题",
                    technical_details={
                        'tested_users': list(response_bodies.keys()),
                        'response_identical': True
                    },
                    evidence=["所有测试用户获得相同响应"],
                    recommendation="检查API是否正确实现了基于用户权限的数据过滤",
                    api_info=api_info
                )
                results.append(result)
            
            # 检查是否有用户获得了更多信息
            response_lengths = {user_id: len(body) for user_id, body in response_bodies.items()}
            max_length = max(response_lengths.values())
            min_length = min(response_lengths.values())
            
            if max_length > min_length * 1.5:  # 响应长度差异较大
                max_user = max(response_lengths, key=response_lengths.get)
                min_user = min(response_lengths, key=response_lengths.get)
                
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.FIELD_EXPOSURE,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title="不同用户响应数据量差异较大",
                    description=f"用户 {max_user} 获得的响应比 {min_user} 多 {max_length - min_length} 字符",
                    technical_details={
                        'response_lengths': response_lengths,
                        'max_user': max_user,
                        'min_user': min_user
                    },
                    evidence=[f"响应长度差异: {max_length} vs {min_length}"],
                    recommendation="检查不同用户权限下的数据返回是否合理",
                    api_info=api_info
                )
                results.append(result)
        
        except Exception as e:
            logger.error(f"比较会话响应失败: {e}")
        
        return results
    
    def _generate_parameter_test_cases(
        self,
        original_params: Dict[str, Any]
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """生成参数测试用例"""
        test_cases = []
        
        # 1. 移除参数测试
        for param_name in original_params.keys():
            modified_params = original_params.copy()
            del modified_params[param_name]
            test_cases.append((f"remove_{param_name}", modified_params))
        
        # 2. 修改敏感参数
        sensitive_params = ['id', 'user_id', 'userId', 'uid', 'account']
        for param_name, param_value in original_params.items():
            if any(sensitive in param_name.lower() for sensitive in sensitive_params):
                # 尝试不同的ID值
                for test_value in ['0', '1', '999999', '-1', 'admin', 'root']:
                    modified_params = original_params.copy()
                    modified_params[param_name] = test_value
                    test_cases.append((f"modify_{param_name}_{test_value}", modified_params))
        
        # 3. 添加新参数
        new_params = [
            ('admin', '1'), ('debug', 'true'), ('test', '1'),
            ('internal', '1'), ('system', '1'), ('bypass', '1')
        ]
        
        for param_name, param_value in new_params:
            modified_params = original_params.copy()
            modified_params[param_name] = param_value
            test_cases.append((f"add_{param_name}", modified_params))
        
        return test_cases
    
    def _compare_parameter_responses(
        self,
        baseline_response: Dict[str, Any],
        test_response: Dict[str, Any],
        test_name: str,
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """比较参数测试的响应"""
        results = []
        
        try:
            baseline_body = baseline_response.get('body', '')
            test_body = test_response.get('body', '')
            
            baseline_status = baseline_response.get('status_code', 200)
            test_status = test_response.get('status_code', 200)
            
            # 检查状态码变化
            if test_status != baseline_status:
                if test_status == 200 and baseline_status != 200:
                    # 参数修改后获得了成功响应
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.PRIVILEGE_ESCALATION,
                        risk_level=RiskLevel.HIGH,
                        is_vulnerable=True,
                        title=f"参数操作绕过访问控制",
                        description=f"通过 {test_name} 操作绕过了访问控制",
                        technical_details={
                            'test_name': test_name,
                            'baseline_status': baseline_status,
                            'test_status': test_status
                        },
                        evidence=[f"状态码变化: {baseline_status} -> {test_status}"],
                        recommendation="检查参数验证和访问控制逻辑",
                        api_info=api_info
                    )
                    results.append(result)
            
            # 检查响应内容变化
            if len(test_body) > len(baseline_body) * 1.2:
                # 获得了更多信息
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.FIELD_EXPOSURE,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title=f"参数操作获得额外信息",
                    description=f"通过 {test_name} 操作获得了额外的响应信息",
                    technical_details={
                        'test_name': test_name,
                        'baseline_length': len(baseline_body),
                        'test_length': len(test_body)
                    },
                    evidence=[f"响应长度增加: {len(baseline_body)} -> {len(test_body)}"],
                    recommendation="检查参数验证是否充分",
                    api_info=api_info
                )
                results.append(result)
        
        except Exception as e:
            logger.error(f"比较参数响应失败: {e}")
        
        return results
