"""
智能Fuzz策略

基于参数特征和风险维度的智能Fuzz测试策略。
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import logging

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ParameterType(Enum):
    """参数类型枚举"""
    ID = "id"                    # ID类参数 (userId, orderId等)
    NUMERIC = "numeric"          # 数值类参数
    STRING = "string"            # 字符串参数
    BOOLEAN = "boolean"          # 布尔参数
    EMAIL = "email"              # 邮箱参数
    PHONE = "phone"              # 手机号参数
    URL = "url"                  # URL参数
    PATH = "path"                # 路径参数
    JSON = "json"                # JSON参数
    XML = "xml"                  # XML参数
    SQL = "sql"                  # SQL相关参数
    COMMAND = "command"          # 命令相关参数
    FILE = "file"                # 文件相关参数
    TOKEN = "token"              # Token类参数
    TIMESTAMP = "timestamp"      # 时间戳参数
    UNKNOWN = "unknown"          # 未知类型


class RiskDimension(Enum):
    """风险维度枚举"""
    INJECTION = "injection"                    # 注入攻击
    AUTHORIZATION = "authorization"            # 权限控制
    DATA_EXPOSURE = "data_exposure"           # 数据泄露
    BUSINESS_LOGIC = "business_logic"         # 业务逻辑
    INPUT_VALIDATION = "input_validation"     # 输入验证
    RATE_LIMITING = "rate_limiting"           # 频率限制
    AUTHENTICATION = "authentication"         # 身份认证
    CRYPTOGRAPHY = "cryptography"             # 加密相关
    FILE_UPLOAD = "file_upload"               # 文件上传
    SSRF = "ssrf"                            # 服务端请求伪造


@dataclass
class ParameterProfile:
    """参数画像"""
    name: str
    value: Any
    param_type: ParameterType
    risk_dimensions: List[RiskDimension]
    sensitivity_score: float  # 敏感度评分 0-10
    fuzz_priority: int        # Fuzz优先级 1-5
    context_hints: List[str]  # 上下文提示


class IntelligentFuzzer:
    """智能Fuzz测试器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化智能Fuzz测试器
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # Fuzz配置
        self.max_fuzz_per_param = self.config.get('max_fuzz_per_param', 20)
        self.enable_deep_fuzz = self.config.get('enable_deep_fuzz', True)
        self.risk_threshold = self.config.get('risk_threshold', 6.0)
        
        # 参数识别模式
        self.param_patterns = self._load_parameter_patterns()
        
        # 风险维度载荷
        self.risk_payloads = self._load_risk_payloads()
        
        # 业务逻辑模式
        self.business_patterns = self._load_business_patterns()
    
    def analyze_parameters(
        self,
        api_info: APIInfo,
        request_params: Dict[str, Any]
    ) -> List[ParameterProfile]:
        """
        分析参数特征，生成参数画像
        
        Args:
            api_info: API信息
            request_params: 请求参数
            
        Returns:
            参数画像列表
        """
        profiles = []
        
        for param_name, param_value in request_params.items():
            try:
                # 识别参数类型
                param_type = self._identify_parameter_type(param_name, param_value)
                
                # 识别风险维度
                risk_dimensions = self._identify_risk_dimensions(
                    param_name, param_value, param_type, api_info
                )
                
                # 计算敏感度评分
                sensitivity_score = self._calculate_sensitivity_score(
                    param_name, param_value, param_type, api_info
                )
                
                # 确定Fuzz优先级
                fuzz_priority = self._calculate_fuzz_priority(
                    param_type, risk_dimensions, sensitivity_score
                )
                
                # 生成上下文提示
                context_hints = self._generate_context_hints(
                    param_name, param_value, api_info
                )
                
                profile = ParameterProfile(
                    name=param_name,
                    value=param_value,
                    param_type=param_type,
                    risk_dimensions=risk_dimensions,
                    sensitivity_score=sensitivity_score,
                    fuzz_priority=fuzz_priority,
                    context_hints=context_hints
                )
                
                profiles.append(profile)
                
            except Exception as e:
                logger.error(f"分析参数 {param_name} 失败: {e}")
                continue
        
        # 按优先级排序
        profiles.sort(key=lambda x: x.fuzz_priority, reverse=True)
        
        return profiles
    
    def generate_intelligent_payloads(
        self,
        profile: ParameterProfile,
        api_info: APIInfo
    ) -> List[Tuple[str, Any, str]]:
        """
        基于参数画像生成智能载荷
        
        Args:
            profile: 参数画像
            api_info: API信息
            
        Returns:
            载荷列表 [(载荷类型, 载荷值, 描述)]
        """
        payloads = []
        
        # 根据风险维度生成载荷
        for risk_dimension in profile.risk_dimensions:
            dimension_payloads = self._generate_dimension_payloads(
                risk_dimension, profile, api_info
            )
            payloads.extend(dimension_payloads)
        
        # 根据参数类型生成载荷
        type_payloads = self._generate_type_specific_payloads(profile, api_info)
        payloads.extend(type_payloads)
        
        # 业务逻辑相关载荷
        if self.enable_deep_fuzz:
            business_payloads = self._generate_business_logic_payloads(profile, api_info)
            payloads.extend(business_payloads)
        
        # 限制载荷数量
        if len(payloads) > self.max_fuzz_per_param:
            # 按风险评分排序，取前N个
            payloads = sorted(payloads, key=lambda x: self._calculate_payload_risk_score(x[0]))
            payloads = payloads[:self.max_fuzz_per_param]
        
        return payloads
    
    def _identify_parameter_type(self, param_name: str, param_value: Any) -> ParameterType:
        """识别参数类型"""
        param_name_lower = param_name.lower()
        param_value_str = str(param_value)
        
        # ID类参数
        if any(pattern in param_name_lower for pattern in ['id', 'uid', 'user_id', 'order_id', 'item_id']):
            return ParameterType.ID
        
        # Token类参数
        if any(pattern in param_name_lower for pattern in ['token', 'key', 'secret', 'auth']):
            return ParameterType.TOKEN
        
        # 邮箱参数
        if 'email' in param_name_lower or re.match(r'^[^@]+@[^@]+\.[^@]+$', param_value_str):
            return ParameterType.EMAIL
        
        # 手机号参数
        if 'phone' in param_name_lower or re.match(r'^1[3-9]\d{9}$', param_value_str):
            return ParameterType.PHONE
        
        # URL参数
        if any(pattern in param_name_lower for pattern in ['url', 'link', 'href']) or \
           param_value_str.startswith(('http://', 'https://', 'ftp://')):
            return ParameterType.URL
        
        # 路径参数
        if any(pattern in param_name_lower for pattern in ['path', 'file', 'dir']) or \
           ('/' in param_value_str and not param_value_str.startswith('http')):
            return ParameterType.PATH
        
        # 时间戳参数
        if any(pattern in param_name_lower for pattern in ['time', 'timestamp', 'date']) or \
           (isinstance(param_value, (int, float)) and param_value > 1000000000):
            return ParameterType.TIMESTAMP
        
        # JSON参数
        if isinstance(param_value, dict) or \
           (isinstance(param_value, str) and param_value.strip().startswith(('{', '['))):
            return ParameterType.JSON
        
        # XML参数
        if isinstance(param_value, str) and param_value.strip().startswith('<'):
            return ParameterType.XML
        
        # 布尔参数
        if isinstance(param_value, bool) or param_value_str.lower() in ['true', 'false', '0', '1']:
            return ParameterType.BOOLEAN
        
        # 数值参数
        if isinstance(param_value, (int, float)) or param_value_str.isdigit():
            return ParameterType.NUMERIC
        
        # SQL相关参数
        if any(keyword in param_value_str.lower() for keyword in ['select', 'insert', 'update', 'delete', 'union']):
            return ParameterType.SQL
        
        # 命令相关参数
        if any(keyword in param_value_str.lower() for keyword in ['cmd', 'exec', 'system', 'shell']):
            return ParameterType.COMMAND
        
        # 默认为字符串
        return ParameterType.STRING
    
    def _identify_risk_dimensions(
        self,
        param_name: str,
        param_value: Any,
        param_type: ParameterType,
        api_info: APIInfo
    ) -> List[RiskDimension]:
        """识别风险维度"""
        dimensions = []
        param_name_lower = param_name.lower()
        param_value_str = str(param_value)
        
        # 基于参数类型的风险维度
        type_dimensions = {
            ParameterType.ID: [RiskDimension.AUTHORIZATION, RiskDimension.DATA_EXPOSURE],
            ParameterType.TOKEN: [RiskDimension.AUTHENTICATION, RiskDimension.AUTHORIZATION],
            ParameterType.EMAIL: [RiskDimension.DATA_EXPOSURE, RiskDimension.INPUT_VALIDATION],
            ParameterType.PHONE: [RiskDimension.DATA_EXPOSURE, RiskDimension.INPUT_VALIDATION],
            ParameterType.URL: [RiskDimension.SSRF, RiskDimension.INPUT_VALIDATION],
            ParameterType.PATH: [RiskDimension.INJECTION, RiskDimension.FILE_UPLOAD],
            ParameterType.JSON: [RiskDimension.INJECTION, RiskDimension.INPUT_VALIDATION],
            ParameterType.XML: [RiskDimension.INJECTION, RiskDimension.INPUT_VALIDATION],
            ParameterType.SQL: [RiskDimension.INJECTION],
            ParameterType.COMMAND: [RiskDimension.INJECTION],
        }
        
        if param_type in type_dimensions:
            dimensions.extend(type_dimensions[param_type])
        
        # 基于参数名的风险维度
        if any(keyword in param_name_lower for keyword in ['admin', 'root', 'super']):
            dimensions.append(RiskDimension.AUTHORIZATION)
        
        if any(keyword in param_name_lower for keyword in ['password', 'pwd', 'secret']):
            dimensions.extend([RiskDimension.AUTHENTICATION, RiskDimension.CRYPTOGRAPHY])
        
        if any(keyword in param_name_lower for keyword in ['file', 'upload', 'image']):
            dimensions.append(RiskDimension.FILE_UPLOAD)
        
        # 基于API路径的风险维度
        api_path_lower = api_info.path.lower()
        if any(keyword in api_path_lower for keyword in ['admin', 'manage', 'control']):
            dimensions.append(RiskDimension.AUTHORIZATION)
        
        if any(keyword in api_path_lower for keyword in ['login', 'auth', 'signin']):
            dimensions.append(RiskDimension.AUTHENTICATION)
        
        if any(keyword in api_path_lower for keyword in ['search', 'query', 'find']):
            dimensions.append(RiskDimension.INJECTION)
        
        # 去重
        return list(set(dimensions))
    
    def _calculate_sensitivity_score(
        self,
        param_name: str,
        param_value: Any,
        param_type: ParameterType,
        api_info: APIInfo
    ) -> float:
        """计算敏感度评分"""
        score = 0.0
        param_name_lower = param_name.lower()
        
        # 基于参数类型的基础分数
        type_scores = {
            ParameterType.ID: 8.0,
            ParameterType.TOKEN: 9.0,
            ParameterType.EMAIL: 7.0,
            ParameterType.PHONE: 7.0,
            ParameterType.URL: 6.0,
            ParameterType.PATH: 7.0,
            ParameterType.JSON: 5.0,
            ParameterType.SQL: 9.0,
            ParameterType.COMMAND: 9.0,
            ParameterType.NUMERIC: 4.0,
            ParameterType.STRING: 3.0,
        }
        
        score += type_scores.get(param_type, 3.0)
        
        # 基于参数名的敏感度加分
        sensitive_keywords = {
            'password': 2.0, 'pwd': 2.0, 'secret': 2.0,
            'admin': 1.5, 'root': 1.5, 'super': 1.5,
            'token': 1.5, 'key': 1.5, 'auth': 1.0,
            'id': 1.0, 'uid': 1.0, 'user_id': 1.0,
            'email': 0.5, 'phone': 0.5, 'mobile': 0.5
        }
        
        for keyword, bonus in sensitive_keywords.items():
            if keyword in param_name_lower:
                score += bonus
        
        # 基于API路径的敏感度调整
        api_path_lower = api_info.path.lower()
        if any(keyword in api_path_lower for keyword in ['admin', 'manage', 'control']):
            score += 1.0
        
        if any(keyword in api_path_lower for keyword in ['user', 'profile', 'account']):
            score += 0.5
        
        # 限制在0-10范围内
        return min(max(score, 0.0), 10.0)
    
    def _calculate_fuzz_priority(
        self,
        param_type: ParameterType,
        risk_dimensions: List[RiskDimension],
        sensitivity_score: float
    ) -> int:
        """计算Fuzz优先级"""
        priority = 1
        
        # 基于敏感度评分
        if sensitivity_score >= 8.0:
            priority = 5
        elif sensitivity_score >= 6.0:
            priority = 4
        elif sensitivity_score >= 4.0:
            priority = 3
        elif sensitivity_score >= 2.0:
            priority = 2
        
        # 基于风险维度数量
        if len(risk_dimensions) >= 3:
            priority = min(priority + 1, 5)
        
        # 特殊参数类型优先级调整
        high_priority_types = [
            ParameterType.ID, ParameterType.TOKEN, 
            ParameterType.SQL, ParameterType.COMMAND
        ]
        
        if param_type in high_priority_types:
            priority = min(priority + 1, 5)
        
        return priority
    
    def _generate_context_hints(
        self,
        param_name: str,
        param_value: Any,
        api_info: APIInfo
    ) -> List[str]:
        """生成上下文提示"""
        hints = []
        param_name_lower = param_name.lower()
        
        # 参数名提示
        if 'id' in param_name_lower:
            hints.append("可能存在越权访问风险")
        
        if any(keyword in param_name_lower for keyword in ['token', 'key', 'auth']):
            hints.append("认证相关参数，需要重点测试")
        
        if any(keyword in param_name_lower for keyword in ['admin', 'root']):
            hints.append("管理员相关参数，高风险")
        
        # API路径提示
        api_path_lower = api_info.path.lower()
        if 'admin' in api_path_lower:
            hints.append("管理员接口，需要测试权限绕过")
        
        if any(keyword in api_path_lower for keyword in ['search', 'query']):
            hints.append("查询接口，需要测试注入攻击")
        
        return hints
    
    def _load_parameter_patterns(self) -> Dict[str, List[str]]:
        """加载参数识别模式"""
        return {
            'id_patterns': [
                r'.*[_-]?id$', r'^id[_-]?.*', r'.*uid.*', r'.*[_-]?key$'
            ],
            'auth_patterns': [
                r'.*token.*', r'.*auth.*', r'.*secret.*', r'.*key.*'
            ],
            'sensitive_patterns': [
                r'.*password.*', r'.*pwd.*', r'.*email.*', r'.*phone.*'
            ]
        }
    
    def _load_risk_payloads(self) -> Dict[RiskDimension, List[Dict[str, Any]]]:
        """加载风险维度载荷"""
        return {
            RiskDimension.INJECTION: [
                {'payload': "' OR '1'='1", 'type': 'sql_injection', 'description': 'SQL注入测试'},
                {'payload': '<script>alert(1)</script>', 'type': 'xss', 'description': 'XSS测试'},
                {'payload': '${7*7}', 'type': 'template_injection', 'description': '模板注入测试'},
                {'payload': '{{7*7}}', 'type': 'template_injection', 'description': 'Jinja2模板注入'},
                {'payload': '; cat /etc/passwd', 'type': 'command_injection', 'description': '命令注入测试'},
            ],
            RiskDimension.AUTHORIZATION: [
                {'payload': '0', 'type': 'id_manipulation', 'description': 'ID为0测试'},
                {'payload': '-1', 'type': 'id_manipulation', 'description': '负数ID测试'},
                {'payload': '999999', 'type': 'id_manipulation', 'description': '大数ID测试'},
                {'payload': 'admin', 'type': 'role_manipulation', 'description': '角色提升测试'},
            ],
            RiskDimension.INPUT_VALIDATION: [
                {'payload': 'A' * 1000, 'type': 'buffer_overflow', 'description': '缓冲区溢出测试'},
                {'payload': '../../../etc/passwd', 'type': 'path_traversal', 'description': '路径遍历测试'},
                {'payload': 'null', 'type': 'null_injection', 'description': 'Null注入测试'},
                {'payload': '', 'type': 'empty_value', 'description': '空值测试'},
            ]
        }
    
    def _load_business_patterns(self) -> Dict[str, List[str]]:
        """加载业务逻辑模式"""
        return {
            'ecommerce': [
                'price', 'amount', 'quantity', 'discount', 'coupon',
                'order', 'cart', 'payment', 'shipping'
            ],
            'user_management': [
                'user', 'profile', 'account', 'login', 'register',
                'password', 'email', 'phone'
            ],
            'admin_functions': [
                'admin', 'manage', 'control', 'config', 'setting',
                'delete', 'modify', 'create'
            ]
        }
    
    def _generate_dimension_payloads(
        self,
        dimension: RiskDimension,
        profile: ParameterProfile,
        api_info: APIInfo
    ) -> List[Tuple[str, Any, str]]:
        """生成特定风险维度的载荷"""
        payloads = []
        
        if dimension in self.risk_payloads:
            for payload_info in self.risk_payloads[dimension]:
                payloads.append((
                    payload_info['type'],
                    payload_info['payload'],
                    payload_info['description']
                ))
        
        return payloads
    
    def _generate_type_specific_payloads(
        self,
        profile: ParameterProfile,
        api_info: APIInfo
    ) -> List[Tuple[str, Any, str]]:
        """生成参数类型特定的载荷"""
        payloads = []
        
        if profile.param_type == ParameterType.ID:
            payloads.extend([
                ('id_enumeration', 1, 'ID枚举测试'),
                ('id_enumeration', 999999, '大数ID测试'),
                ('id_manipulation', '0', 'ID为0测试'),
                ('id_manipulation', '-1', '负数ID测试'),
                ('id_manipulation', 'admin', '字符串ID测试'),
            ])
        
        elif profile.param_type == ParameterType.NUMERIC:
            payloads.extend([
                ('boundary_test', 0, '边界值0测试'),
                ('boundary_test', -1, '负数边界测试'),
                ('boundary_test', 2147483647, '整数最大值测试'),
                ('boundary_test', -2147483648, '整数最小值测试'),
                ('type_confusion', 'abc', '类型混淆测试'),
            ])
        
        elif profile.param_type == ParameterType.STRING:
            payloads.extend([
                ('length_test', 'A' * 1000, '长字符串测试'),
                ('special_chars', '!@#$%^&*()', '特殊字符测试'),
                ('unicode_test', '测试中文字符', 'Unicode测试'),
                ('null_byte', 'test\x00', 'Null字节测试'),
            ])
        
        return payloads
    
    def _generate_business_logic_payloads(
        self,
        profile: ParameterProfile,
        api_info: APIInfo
    ) -> List[Tuple[str, Any, str]]:
        """生成业务逻辑相关载荷"""
        payloads = []
        param_name_lower = profile.name.lower()
        api_path_lower = api_info.path.lower()
        
        # 电商相关业务逻辑测试
        if any(keyword in param_name_lower for keyword in ['price', 'amount', 'money']):
            payloads.extend([
                ('business_logic', -1, '负价格测试'),
                ('business_logic', 0, '零价格测试'),
                ('business_logic', 0.01, '极小价格测试'),
                ('business_logic', 999999999, '极大价格测试'),
            ])
        
        if any(keyword in param_name_lower for keyword in ['quantity', 'count', 'num']):
            payloads.extend([
                ('business_logic', -1, '负数量测试'),
                ('business_logic', 0, '零数量测试'),
                ('business_logic', 999999, '极大数量测试'),
            ])
        
        # 用户相关业务逻辑测试
        if 'user' in api_path_lower and 'id' in param_name_lower:
            payloads.extend([
                ('horizontal_privilege', '1', '其他用户ID测试'),
                ('horizontal_privilege', '999999', '不存在用户ID测试'),
            ])
        
        return payloads
    
    def _calculate_payload_risk_score(self, payload_type: str) -> float:
        """计算载荷风险评分"""
        risk_scores = {
            'sql_injection': 9.0,
            'command_injection': 9.0,
            'xss': 7.0,
            'template_injection': 8.0,
            'id_manipulation': 8.0,
            'path_traversal': 7.0,
            'buffer_overflow': 6.0,
            'business_logic': 7.0,
            'horizontal_privilege': 8.0,
            'boundary_test': 5.0,
            'type_confusion': 6.0,
        }
        
        return risk_scores.get(payload_type, 5.0)
