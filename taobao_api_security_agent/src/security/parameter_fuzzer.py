"""
参数Fuzz测试模块

对API参数进行Fuzz测试，检测各种安全漏洞。
"""

import random
import string
from typing import Dict, List, Optional, Any, Iterator, Tuple
import logging
from datetime import datetime

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ParameterFuzzer:
    """参数Fuzz测试器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化参数Fuzz测试器
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # Fuzz测试配置
        self.max_fuzz_iterations = self.config.get('max_fuzz_iterations', 50)
        self.fuzz_string_length = self.config.get('fuzz_string_length', 100)
        
        # 加载Fuzz测试载荷
        self.payloads = self._load_fuzz_payloads()
        
        # 敏感参数名称
        self.sensitive_params = [
            'id', 'user_id', 'userId', 'uid', 'account', 'username',
            'password', 'token', 'key', 'secret', 'admin', 'role'
        ]
    
    def generate_fuzz_parameters(
        self,
        original_params: Dict[str, Any],
        fuzz_types: Optional[List[str]] = None
    ) -> Iterator[Tuple[str, Dict[str, Any]]]:
        """
        生成Fuzz测试参数
        
        Args:
            original_params: 原始参数
            fuzz_types: 要进行的Fuzz测试类型
            
        Yields:
            (测试类型, 修改后的参数)
        """
        if not fuzz_types:
            fuzz_types = [
                'buffer_overflow', 'format_string', 'null_byte', 'unicode',
                'boundary_values', 'type_confusion', 'parameter_pollution'
            ]
        
        iteration_count = 0
        
        for fuzz_type in fuzz_types:
            if iteration_count >= self.max_fuzz_iterations:
                break
            
            # 为每个参数生成Fuzz测试用例
            for param_name, param_value in original_params.items():
                if iteration_count >= self.max_fuzz_iterations:
                    break
                
                fuzz_cases = self._generate_fuzz_cases_for_param(
                    param_name, param_value, fuzz_type
                )
                
                for fuzz_case in fuzz_cases:
                    if iteration_count >= self.max_fuzz_iterations:
                        break
                    
                    modified_params = original_params.copy()
                    modified_params[param_name] = fuzz_case
                    
                    yield f"{fuzz_type}_{param_name}", modified_params
                    iteration_count += 1
            
            # 生成新参数的Fuzz测试
            if iteration_count < self.max_fuzz_iterations:
                new_param_cases = self._generate_new_parameter_fuzz(fuzz_type)
                for param_name, param_value in new_param_cases:
                    if iteration_count >= self.max_fuzz_iterations:
                        break
                    
                    modified_params = original_params.copy()
                    modified_params[param_name] = param_value
                    
                    yield f"{fuzz_type}_new_param", modified_params
                    iteration_count += 1
    
    def _generate_fuzz_cases_for_param(
        self,
        param_name: str,
        param_value: Any,
        fuzz_type: str
    ) -> List[Any]:
        """为特定参数生成Fuzz测试用例"""
        cases = []
        
        if fuzz_type == 'buffer_overflow':
            # 生成长字符串
            cases.extend([
                'A' * 100,
                'A' * 1000,
                'A' * 10000,
                '\x00' * 100,
                '\xff' * 100
            ])
            
        elif fuzz_type == 'format_string':
            cases.extend(self.payloads['format_string'])
            
        elif fuzz_type == 'null_byte':
            cases.extend([
                f"{param_value}\x00",
                f"\x00{param_value}",
                f"{param_value}\x00.txt",
                "\x00" * 10
            ])
            
        elif fuzz_type == 'unicode':
            cases.extend([
                '测试中文',
                '🚀🔥💯',
                '\u0000\u0001\u0002',
                '\uffff\ufffe\ufffd'
            ])
            
        elif fuzz_type == 'boundary_values':
            cases.extend(self._generate_boundary_values(param_value))
            
        elif fuzz_type == 'type_confusion':
            cases.extend(self._generate_type_confusion_values(param_value))
            
        elif fuzz_type == 'parameter_pollution':
            # 参数污染通过修改参数字典结构实现
            cases.append(param_value)
        
        return cases
    
    def _generate_new_parameter_fuzz(self, fuzz_type: str) -> List[Tuple[str, Any]]:
        """生成新参数的Fuzz测试"""
        new_params = []
        
        # 常见的敏感参数名
        param_names = [
            'admin', 'debug', 'test', 'dev', 'internal', 'system',
            'root', 'superuser', 'bypass', 'override', 'force'
        ]
        
        for param_name in param_names:
            if fuzz_type == 'privilege_escalation':
                new_params.extend([
                    (param_name, '1'),
                    (param_name, 'true'),
                    (param_name, 'yes'),
                    (f"is_{param_name}", '1'),
                    (f"{param_name}_mode", 'on')
                ])
            
            elif fuzz_type == 'parameter_injection':
                new_params.extend([
                    (f"__{param_name}__", '1'),
                    (f"x-{param_name}", '1'),
                    (f"{param_name}[]", '1'),
                    (f"{param_name}[0]", '1')
                ])
        
        return new_params
    
    def _generate_boundary_values(self, original_value: Any) -> List[Any]:
        """生成边界值测试用例"""
        boundary_values = []
        
        # 数值边界值
        if isinstance(original_value, (int, str)) and str(original_value).isdigit():
            boundary_values.extend([
                -1, 0, 1,
                2147483647, 2147483648,  # 32位整数边界
                -2147483648, -2147483649,
                9223372036854775807, 9223372036854775808,  # 64位整数边界
                '0', '-1', '999999999999999999999'
            ])
        
        # 字符串长度边界值
        if isinstance(original_value, str):
            boundary_values.extend([
                '',  # 空字符串
                'a',  # 单字符
                'a' * 255,  # 常见最大长度
                'a' * 256,  # 超过常见最大长度
                'a' * 65535,  # 更大的长度
            ])
        
        return boundary_values
    
    def _generate_type_confusion_values(self, original_value: Any) -> List[Any]:
        """生成类型混淆测试用例"""
        type_values = []
        
        # 不同类型的值
        type_values.extend([
            None,
            True, False,
            0, -1, 1,
            0.0, -1.1, 1.1,
            '',
            [],
            {},
            'null', 'undefined', 'NaN',
            '[]', '{}', 'true', 'false'
        ])
        
        return type_values
    
    def _load_fuzz_payloads(self) -> Dict[str, List[str]]:
        """加载Fuzz测试载荷"""
        payloads = {
            
            'format_string': [
                "%s%s%s%s%s%s%s%s%s%s",
                "%x%x%x%x%x%x%x%x%x%x",
                "%n%n%n%n%n%n%n%n%n%n",
                "%08x.%08x.%08x.%08x",
                "%d%d%d%d%d%d%d%d%d%d",
                "%.1000d%.1000d%.1000d",
                "%99999999999s",
                "%08x%08x%08x%08x%08x%08x%08x%08x"
            ]
        }
        
        return payloads
    
    def analyze_fuzz_response(
        self,
        original_response: Dict[str, Any],
        fuzz_response: Dict[str, Any],
        fuzz_type: str,
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """
        分析Fuzz测试响应
        
        Args:
            original_response: 原始响应
            fuzz_response: Fuzz测试响应
            fuzz_type: Fuzz测试类型
            api_info: API信息
            
        Returns:
            安全检查结果列表
        """
        results = []
        
        try:
            # 比较状态码
            original_status = original_response.get('status_code', 200)
            fuzz_status = fuzz_response.get('status_code', 200)
            
            # 比较响应体
            original_body = original_response.get('body', '')
            fuzz_body = fuzz_response.get('body', '')
            
            # 比较响应时间
            original_time = original_response.get('response_time', 0)
            fuzz_time = fuzz_response.get('response_time', 0)
            
            # 检测异常状态码
            if fuzz_status != original_status:
                if fuzz_status == 500:
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.OWASP_API8_SECURITY_MISCONFIGURATION,
                        risk_level=RiskLevel.MEDIUM,
                        is_vulnerable=True,
                        title=f"{fuzz_type} 导致服务器错误",
                        description=f"Fuzz测试 {fuzz_type} 导致服务器返回500错误",
                        technical_details={
                            'fuzz_type': fuzz_type,
                            'original_status': original_status,
                            'fuzz_status': fuzz_status
                        },
                        evidence=[f"状态码变化: {original_status} -> {fuzz_status}"],
                        recommendation="检查输入验证和错误处理机制",
                        api_info=api_info
                    )
                    results.append(result)
            
            # 检测响应时间异常（可能的DoS）
            if fuzz_time > original_time * 3 and fuzz_time > 5:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.OWASP_API4_UNRESTRICTED,
                    risk_level=RiskLevel.MEDIUM,
                    is_vulnerable=True,
                    title=f"{fuzz_type} 导致响应时间异常",
                    description=f"Fuzz测试 {fuzz_type} 导致响应时间显著增加",
                    technical_details={
                        'fuzz_type': fuzz_type,
                        'original_time': original_time,
                        'fuzz_time': fuzz_time,
                        'time_increase': fuzz_time / original_time if original_time > 0 else 'N/A'
                    },
                    evidence=[f"响应时间增加: {original_time:.2f}s -> {fuzz_time:.2f}s"],
                    recommendation="检查是否存在资源消耗攻击漏洞",
                    api_info=api_info
                )
                results.append(result)
            
            # 检测响应内容变化
            if len(fuzz_body) != len(original_body):
                # 检查是否泄露了额外信息
                if len(fuzz_body) > len(original_body) * 1.5:
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.FIELD_EXPOSURE,
                        risk_level=RiskLevel.LOW,
                        is_vulnerable=True,
                        title=f"{fuzz_type} 导致响应内容异常",
                        description=f"Fuzz测试 {fuzz_type} 导致响应内容显著增加",
                        technical_details={
                            'fuzz_type': fuzz_type,
                            'original_length': len(original_body),
                            'fuzz_length': len(fuzz_body)
                        },
                        evidence=[f"响应长度变化: {len(original_body)} -> {len(fuzz_body)}"],
                        recommendation="检查是否泄露了额外的调试信息或错误详情",
                        api_info=api_info
                    )
                    results.append(result)
            
            # 检测特定的错误模式
            error_patterns = [
                'SQL syntax error', 'mysql_fetch', 'ORA-', 'Microsoft OLE DB',
                'java.sql.SQLException', 'PostgreSQL query failed',
                'Warning: mysql_', 'valid MySQL result', 'MySqlClient',
                'Traceback', 'stack trace', 'Exception in thread',
                'Internal Server Error', 'Debug mode'
            ]
            
            for pattern in error_patterns:
                if pattern.lower() in fuzz_body.lower() and pattern.lower() not in original_body.lower():
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.FIELD_EXPOSURE,
                        risk_level=RiskLevel.HIGH,
                        is_vulnerable=True,
                        title=f"{fuzz_type} 导致错误信息泄露",
                        description=f"Fuzz测试 {fuzz_type} 导致泄露敏感错误信息",
                        technical_details={
                            'fuzz_type': fuzz_type,
                            'error_pattern': pattern,
                            'response_snippet': fuzz_body[:200] + '...' if len(fuzz_body) > 200 else fuzz_body
                        },
                        evidence=[f"检测到错误模式: {pattern}"],
                        recommendation="配置适当的错误处理，避免泄露敏感的系统信息",
                        api_info=api_info
                    )
                    results.append(result)
                    break
        
        except Exception as e:
            logger.error(f"分析Fuzz响应失败: {e}")
        
        return results
