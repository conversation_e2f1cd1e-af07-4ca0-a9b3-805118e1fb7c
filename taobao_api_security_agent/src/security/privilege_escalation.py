"""
越权访问检测器

检测API是否存在水平越权和垂直越权漏洞。
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime

from ..agent.state import APIInfo, SecurityCheckResult, VulnerabilityType, RiskLevel, SessionInfo
from .request_replayer import RequestReplayer
from .response_analyzer import ResponseAnalyzer
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PrivilegeEscalationDetector:
    """越权访问检测器"""
    
    def __init__(
        self,
        request_replayer: RequestReplayer,
        response_analyzer: ResponseAnalyzer,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化越权访问检测器
        
        Args:
            request_replayer: 请求重放器
            response_analyzer: 响应分析器
            config: 配置信息
        """
        self.request_replayer = request_replayer
        self.response_analyzer = response_analyzer
        self.config = config or {}
        
        # 检测配置
        self.test_horizontal_escalation = self.config.get('test_horizontal_escalation', True)
        self.test_vertical_escalation = self.config.get('test_vertical_escalation', True)
        self.max_user_id_tests = self.config.get('max_user_id_tests', 10)
        
        # 用户ID测试范围
        self.user_id_test_values = self._generate_user_id_test_values()
    
    async def detect_privilege_escalation(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """
        检测越权访问
        
        Args:
            api_info: API信息
            sessions: 可用的会话列表
            
        Returns:
            安全检查结果列表
        """
        results = []
        
        try:
            logger.info(f"开始检测越权访问: {api_info.url}")
            
            # 1. 水平越权检测
            if self.test_horizontal_escalation:
                horizontal_results = await self._test_horizontal_privilege_escalation(
                    api_info, sessions
                )
                results.extend(horizontal_results)
            
            # 2. 垂直越权检测
            if self.test_vertical_escalation:
                vertical_results = await self._test_vertical_privilege_escalation(
                    api_info, sessions
                )
                results.extend(vertical_results)
            
            # 3. 匿名访问测试
            anonymous_results = await self._test_anonymous_access(api_info)
            results.extend(anonymous_results)
            
            # 4. 参数篡改测试
            parameter_results = await self._test_parameter_tampering(
                api_info, sessions[0] if sessions else None
            )
            results.extend(parameter_results)
            
            logger.info(f"越权访问检测完成，发现 {len(results)} 个问题")
            
        except Exception as e:
            logger.error(f"越权访问检测失败: {e}")
        
        return results
    
    async def _test_horizontal_privilege_escalation(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试水平越权（同级用户间的越权）"""
        results = []
        
        try:
            if len(sessions) < 2:
                logger.warning("会话数量不足，跳过水平越权测试")
                return results
            
            # 使用不同用户会话测试相同的API
            responses = {}
            
            for session in sessions:
                success, response_data = await self.request_replayer.replay_request(
                    api_info, session
                )
                
                if success:
                    responses[session.user_id] = {
                        'session': session,
                        'response': response_data['response']
                    }
            
            # 分析不同用户的响应
            if len(responses) >= 2:
                horizontal_results = self._analyze_horizontal_responses(
                    responses, api_info
                )
                results.extend(horizontal_results)
            
            # 测试用户ID参数篡改
            if api_info.request_params:
                id_tampering_results = await self._test_user_id_tampering(
                    api_info, sessions[0] if sessions else None
                )
                results.extend(id_tampering_results)
        
        except Exception as e:
            logger.error(f"水平越权测试失败: {e}")
        
        return results
    
    async def _test_vertical_privilege_escalation(
        self,
        api_info: APIInfo,
        sessions: List[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试垂直越权（低权限用户访问高权限功能）"""
        results = []
        
        try:
            # 按用户类型分组
            user_types = {}
            for session in sessions:
                user_type = session.user_type
                if user_type not in user_types:
                    user_types[user_type] = []
                user_types[user_type].append(session)
            
            # 定义权限等级
            privilege_levels = {
                'anonymous': 0,
                'normal': 1,
                'vip': 2,
                'seller': 3,
                'admin': 4
            }
            
            # 测试低权限用户是否能访问高权限功能
            for user_type, type_sessions in user_types.items():
                user_level = privilege_levels.get(user_type, 1)
                
                # 检查是否能访问管理员功能
                if user_level < 4 and self._is_admin_api(api_info):
                    for session in type_sessions[:1]:  # 只测试第一个会话
                        success, response_data = await self.request_replayer.replay_request(
                            api_info, session
                        )
                        
                        if success and self._is_successful_response(response_data['response']):
                            result = SecurityCheckResult(
                                check_type=VulnerabilityType.OWASP_API5_BFLA,
                                risk_level=RiskLevel.HIGH,
                                is_vulnerable=True,
                                title="垂直越权访问",
                                description=f"{user_type} 用户能够访问管理员功能",
                                technical_details={
                                    'user_type': user_type,
                                    'user_level': user_level,
                                    'api_url': api_info.url,
                                    'response_status': response_data['response'].get('status_code')
                                },
                                evidence=[f"{user_type} 用户成功访问管理员API"],
                                recommendation="实施严格的基于角色的访问控制",
                                api_info=api_info
                            )
                            results.append(result)
                
                # 测试权限提升参数
                privilege_results = await self._test_privilege_elevation_parameters(
                    api_info, type_sessions[0] if type_sessions else None, user_type
                )
                results.extend(privilege_results)
        
        except Exception as e:
            logger.error(f"垂直越权测试失败: {e}")
        
        return results
    
    async def _test_anonymous_access(self, api_info: APIInfo) -> List[SecurityCheckResult]:
        """测试匿名访问"""
        results = []
        
        try:
            # 不使用任何会话信息进行请求
            success, response_data = await self.request_replayer.replay_request(
                api_info, session=None
            )
            
            if success and self._is_successful_response(response_data['response']):
                # 检查是否应该需要认证
                if self._should_require_authentication(api_info):
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.OWASP_API2_BROKEN_AUTH,
                        risk_level=RiskLevel.HIGH,
                        is_vulnerable=True,
                        title="缺少身份认证",
                        description="API允许匿名访问，但应该需要身份认证",
                        technical_details={
                            'api_url': api_info.url,
                            'response_status': response_data['response'].get('status_code'),
                            'response_size': len(response_data['response'].get('body', ''))
                        },
                        evidence=["匿名请求获得成功响应"],
                        recommendation="为敏感API添加身份认证要求",
                        api_info=api_info
                    )
                    results.append(result)
        
        except Exception as e:
            logger.error(f"匿名访问测试失败: {e}")
        
        return results
    
    async def _test_parameter_tampering(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试参数篡改"""
        results = []
        
        try:
            original_params = api_info.request_params.copy()
            
            # 生成参数篡改测试用例
            tampering_cases = self._generate_tampering_cases(original_params)
            
            # 获取基准响应
            success, baseline_response = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if not success:
                return results
            
            # 执行参数篡改测试
            for case_name, modified_params in tampering_cases:
                try:
                    success, test_response = await self.request_replayer.replay_request(
                        api_info, session, modified_params=modified_params
                    )
                    
                    if success:
                        tampering_results = self._analyze_tampering_response(
                            baseline_response['response'],
                            test_response['response'],
                            case_name,
                            api_info
                        )
                        results.extend(tampering_results)
                
                except Exception as e:
                    logger.warning(f"参数篡改测试 {case_name} 失败: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"参数篡改测试失败: {e}")
        
        return results
    
    async def _test_user_id_tampering(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo]
    ) -> List[SecurityCheckResult]:
        """测试用户ID篡改"""
        results = []
        
        try:
            original_params = api_info.request_params.copy()
            
            # 查找可能的用户ID参数
            user_id_params = self._find_user_id_parameters(original_params)
            
            if not user_id_params:
                return results
            
            # 获取基准响应
            success, baseline_response = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if not success:
                return results
            
            # 测试不同的用户ID值
            for param_name in user_id_params:
                original_value = original_params[param_name]
                
                for test_value in self.user_id_test_values[:self.max_user_id_tests]:
                    if str(test_value) == str(original_value):
                        continue
                    
                    modified_params = original_params.copy()
                    modified_params[param_name] = test_value
                    
                    try:
                        success, test_response = await self.request_replayer.replay_request(
                            api_info, session, modified_params=modified_params
                        )
                        
                        if success and self._is_successful_response(test_response['response']):
                            # 检查是否获得了其他用户的数据
                            if self._indicates_different_user_data(
                                baseline_response['response'],
                                test_response['response']
                            ):
                                result = SecurityCheckResult(
                                    check_type=VulnerabilityType.OWASP_API1_BOLA,
                                    risk_level=RiskLevel.HIGH,
                                    is_vulnerable=True,
                                    title="水平越权访问其他用户数据",
                                    description=f"通过修改参数 {param_name} 访问了其他用户的数据",
                                    technical_details={
                                        'parameter_name': param_name,
                                        'original_value': original_value,
                                        'test_value': test_value,
                                        'response_status': test_response['response'].get('status_code')
                                    },
                                    evidence=[f"参数 {param_name}: {original_value} -> {test_value}"],
                                    recommendation="实施严格的对象级访问控制",
                                    api_info=api_info
                                )
                                results.append(result)
                    
                    except Exception as e:
                        logger.warning(f"用户ID篡改测试失败: {param_name}={test_value}, 错误: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"用户ID篡改测试失败: {e}")
        
        return results
    
    async def _test_privilege_elevation_parameters(
        self,
        api_info: APIInfo,
        session: Optional[SessionInfo],
        user_type: str
    ) -> List[SecurityCheckResult]:
        """测试权限提升参数"""
        results = []
        
        try:
            original_params = api_info.request_params.copy()
            
            # 权限提升参数
            elevation_params = [
                ('admin', '1'), ('admin', 'true'), ('admin', 'yes'),
                ('is_admin', '1'), ('is_admin', 'true'),
                ('role', 'admin'), ('role', 'administrator'),
                ('privilege', 'admin'), ('privilege', 'high'),
                ('level', '999'), ('level', 'admin'),
                ('type', 'admin'), ('type', 'system'),
                ('debug', '1'), ('debug', 'true'),
                ('test', '1'), ('internal', '1')
            ]
            
            # 获取基准响应
            success, baseline_response = await self.request_replayer.replay_request(
                api_info, session
            )
            
            if not success:
                return results
            
            # 测试权限提升参数
            for param_name, param_value in elevation_params:
                modified_params = original_params.copy()
                modified_params[param_name] = param_value
                
                try:
                    success, test_response = await self.request_replayer.replay_request(
                        api_info, session, modified_params=modified_params
                    )
                    
                    if success:
                        # 检查是否获得了更多权限
                        if self._indicates_privilege_elevation(
                            baseline_response['response'],
                            test_response['response']
                        ):
                            result = SecurityCheckResult(
                                check_type=VulnerabilityType.OWASP_API5_BFLA,
                                risk_level=RiskLevel.HIGH,
                                is_vulnerable=True,
                                title="通过参数提升权限",
                                description=f"{user_type} 用户通过添加参数 {param_name}={param_value} 提升了权限",
                                technical_details={
                                    'user_type': user_type,
                                    'parameter_name': param_name,
                                    'parameter_value': param_value,
                                    'baseline_status': baseline_response['response'].get('status_code'),
                                    'test_status': test_response['response'].get('status_code')
                                },
                                evidence=[f"添加参数 {param_name}={param_value} 后获得更多权限"],
                                recommendation="移除或正确验证权限控制参数",
                                api_info=api_info
                            )
                            results.append(result)
                
                except Exception as e:
                    logger.warning(f"权限提升参数测试失败: {param_name}={param_value}, 错误: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"权限提升参数测试失败: {e}")
        
        return results
    
    def _generate_user_id_test_values(self) -> List[Any]:
        """生成用户ID测试值"""
        test_values = []
        
        # 数字ID
        test_values.extend([
            0, 1, 2, 3, 10, 100, 999, 1000, 9999,
            -1, -10, 999999, 1000000
        ])
        
        # 字符串ID
        test_values.extend([
            'admin', 'administrator', 'root', 'system',
            'test', 'guest', 'anonymous', 'user',
            '********-0000-0000-0000-************',
            'ffffffff-ffff-ffff-ffff-ffffffffffff'
        ])
        
        # 特殊值
        test_values.extend([
            None, '', 'null', 'undefined'
        ])
        
        return test_values
    
    def _find_user_id_parameters(self, params: Dict[str, Any]) -> List[str]:
        """查找可能的用户ID参数"""
        user_id_indicators = [
            'id', 'user_id', 'userid', 'uid', 'user',
            'account', 'account_id', 'member_id', 'customer_id'
        ]
        
        found_params = []
        for param_name in params.keys():
            param_lower = param_name.lower()
            if any(indicator in param_lower for indicator in user_id_indicators):
                found_params.append(param_name)
        
        return found_params
    
    def _generate_tampering_cases(self, original_params: Dict[str, Any]) -> List[Tuple[str, Dict[str, Any]]]:
        """生成参数篡改测试用例"""
        cases = []
        
        # 1. 移除所有参数
        cases.append(("remove_all_params", {}))
        
        # 2. 添加恶意参数
        malicious_params = original_params.copy()
        malicious_params.update({
            'admin': '1',
            'debug': 'true',
            'bypass': '1',
            'internal': '1'
        })
        cases.append(("add_malicious_params", malicious_params))
        
        # 3. 修改数值参数
        for param_name, param_value in original_params.items():
            if isinstance(param_value, (int, str)) and str(param_value).isdigit():
                # 尝试负数
                modified_params = original_params.copy()
                modified_params[param_name] = -int(param_value)
                cases.append((f"negative_{param_name}", modified_params))
                
                # 尝试零
                modified_params = original_params.copy()
                modified_params[param_name] = 0
                cases.append((f"zero_{param_name}", modified_params))
        
        return cases
    
    def _analyze_horizontal_responses(
        self,
        responses: Dict[str, Dict[str, Any]],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """分析水平越权响应"""
        results = []
        
        try:
            # 检查是否所有用户都能成功访问
            successful_users = []
            for user_id, data in responses.items():
                if self._is_successful_response(data['response']):
                    successful_users.append(user_id)
            
            if len(successful_users) > 1:
                # 多个用户都能访问，检查是否应该有访问限制
                if self._should_have_user_restrictions(api_info):
                    result = SecurityCheckResult(
                        check_type=VulnerabilityType.OWASP_API1_BOLA,
                        risk_level=RiskLevel.MEDIUM,
                        is_vulnerable=True,
                        title="多用户可访问相同资源",
                        description=f"多个用户 ({', '.join(successful_users)}) 都能访问相同的API资源",
                        technical_details={
                            'successful_users': successful_users,
                            'total_tested_users': len(responses)
                        },
                        evidence=[f"成功访问的用户: {', '.join(successful_users)}"],
                        recommendation="检查是否需要实施用户级别的访问控制",
                        api_info=api_info
                    )
                    results.append(result)
        
        except Exception as e:
            logger.error(f"分析水平越权响应失败: {e}")
        
        return results
    
    def _analyze_tampering_response(
        self,
        baseline_response: Dict[str, Any],
        test_response: Dict[str, Any],
        case_name: str,
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """分析参数篡改响应"""
        results = []
        
        try:
            baseline_status = baseline_response.get('status_code', 200)
            test_status = test_response.get('status_code', 200)
            
            # 检查是否通过参数篡改绕过了访问控制
            if baseline_status != 200 and test_status == 200:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.OWASP_API2_BROKEN_AUTH,
                    risk_level=RiskLevel.HIGH,
                    is_vulnerable=True,
                    title="参数篡改绕过访问控制",
                    description=f"通过 {case_name} 绕过了访问控制",
                    technical_details={
                        'tampering_case': case_name,
                        'baseline_status': baseline_status,
                        'test_status': test_status
                    },
                    evidence=[f"状态码变化: {baseline_status} -> {test_status}"],
                    recommendation="加强参数验证和访问控制",
                    api_info=api_info
                )
                results.append(result)
        
        except Exception as e:
            logger.error(f"分析参数篡改响应失败: {e}")
        
        return results
    
    def _is_admin_api(self, api_info: APIInfo) -> bool:
        """判断是否为管理员API"""
        admin_indicators = [
            '/admin/', '/administrator/', '/manage/', '/management/',
            '/system/', '/internal/', '/backend/'
        ]
        
        url_lower = api_info.url.lower()
        path_lower = api_info.path.lower()
        
        return any(indicator in url_lower or indicator in path_lower 
                  for indicator in admin_indicators)
    
    def _is_successful_response(self, response: Dict[str, Any]) -> bool:
        """判断响应是否成功"""
        status_code = response.get('status_code', 0)
        return 200 <= status_code < 300
    
    def _should_require_authentication(self, api_info: APIInfo) -> bool:
        """判断API是否应该需要认证"""
        sensitive_indicators = [
            '/user/', '/profile/', '/account/', '/order/',
            '/payment/', '/admin/', '/manage/', '/private/'
        ]
        
        url_lower = api_info.url.lower()
        return any(indicator in url_lower for indicator in sensitive_indicators)
    
    def _should_have_user_restrictions(self, api_info: APIInfo) -> bool:
        """判断API是否应该有用户级别的访问限制"""
        user_specific_indicators = [
            '/user/', '/profile/', '/account/', '/order/',
            '/personal/', '/private/', '/my'
        ]
        
        url_lower = api_info.url.lower()
        return any(indicator in url_lower for indicator in user_specific_indicators)
    
    def _indicates_different_user_data(
        self,
        baseline_response: Dict[str, Any],
        test_response: Dict[str, Any]
    ) -> bool:
        """判断是否获得了不同用户的数据"""
        baseline_body = baseline_response.get('body', '')
        test_body = test_response.get('body', '')
        
        # 简单的启发式检查
        return (
            len(test_body) > 0 and
            test_body != baseline_body and
            len(test_body) > len(baseline_body) * 0.5
        )
    
    def _indicates_privilege_elevation(
        self,
        baseline_response: Dict[str, Any],
        test_response: Dict[str, Any]
    ) -> bool:
        """判断是否提升了权限"""
        baseline_status = baseline_response.get('status_code', 200)
        test_status = test_response.get('status_code', 200)
        
        baseline_body = baseline_response.get('body', '')
        test_body = test_response.get('body', '')
        
        # 检查状态码改善或响应内容增加
        return (
            (baseline_status != 200 and test_status == 200) or
            (test_status == 200 and len(test_body) > len(baseline_body) * 1.2)
        )
