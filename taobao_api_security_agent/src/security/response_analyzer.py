"""
响应分析器

分析HTTP响应内容，检测敏感信息泄露。
"""

import re
import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any, Set, Tuple
import logging
from datetime import datetime

from ..agent.state import SecurityCheckResult, VulnerabilityType, RiskLevel, APIInfo
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ResponseAnalyzer:
    """响应分析器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化响应分析器
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 敏感信息检测规则
        self.sensitive_patterns = self._load_sensitive_patterns()
        
        # 敏感字段关键词
        self.sensitive_keywords = self.config.get('sensitive_keywords', [
            'password', 'token', 'secret', 'key', 'phone', 'mobile', 'email',
            'address', 'idcard', 'bankcard', 'realname', 'userid', 'username'
        ])
        
        # 检测深度
        self.max_depth = self.config.get('max_depth', 5)
    
    def analyze_response(
        self,
        response_data: Dict[str, Any],
        api_info: APIInfo
    ) -> List[SecurityCheckResult]:
        """
        分析响应数据
        
        Args:
            response_data: 响应数据
            api_info: API信息
            
        Returns:
            安全检查结果列表
        """
        results = []
        
        try:
            response_body = response_data.get('body', '')
            response_headers = response_data.get('headers', {})
            status_code = response_data.get('status_code', 0)
            
            # 检测响应体中的敏感信息
            body_results = self._analyze_response_body(response_body, api_info)
            results.extend(body_results)
            
            # 检测响应头中的敏感信息
            header_results = self._analyze_response_headers(response_headers, api_info)
            results.extend(header_results)
            
            # 检测状态码异常
            status_results = self._analyze_status_code(status_code, api_info)
            results.extend(status_results)
            
            logger.debug(f"响应分析完成，发现 {len(results)} 个问题")
            
        except Exception as e:
            logger.error(f"响应分析失败: {e}")
        
        return results
    
    def _analyze_response_body(self, body: str, api_info: APIInfo) -> List[SecurityCheckResult]:
        """分析响应体"""
        results = []
        
        if not body:
            return results
        
        try:
            # 尝试解析JSON
            if self._is_json(body):
                json_data = json.loads(body)
                results.extend(self._analyze_json_data(json_data, api_info))
            
            # 尝试解析XML
            elif self._is_xml(body):
                results.extend(self._analyze_xml_data(body, api_info))
            
            # 分析纯文本
            else:
                results.extend(self._analyze_text_data(body, api_info))
            
            # 使用正则表达式检测敏感模式
            pattern_results = self._detect_sensitive_patterns(body, api_info)
            results.extend(pattern_results)
            
        except Exception as e:
            logger.error(f"分析响应体失败: {e}")
        
        return results
    
    def _analyze_json_data(self, data: Any, api_info: APIInfo, path: str = "") -> List[SecurityCheckResult]:
        """分析JSON数据"""
        results = []
        
        if len(path.split('.')) > self.max_depth:
            return results
        
        try:
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查键名是否敏感
                    if self._is_sensitive_key(key):
                        result = self._create_sensitive_field_result(
                            key, value, current_path, api_info
                        )
                        if result:
                            results.append(result)
                    
                    # 递归检查值
                    if isinstance(value, (dict, list)):
                        results.extend(self._analyze_json_data(value, api_info, current_path))
                    elif isinstance(value, str):
                        # 检查值是否包含敏感信息
                        sensitive_info = self._detect_sensitive_value(value)
                        if sensitive_info:
                            result = self._create_sensitive_value_result(
                                key, value, sensitive_info, current_path, api_info
                            )
                            if result:
                                results.append(result)
            
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    results.extend(self._analyze_json_data(item, api_info, current_path))
        
        except Exception as e:
            logger.error(f"分析JSON数据失败: {e}")
        
        return results
    
    def _analyze_xml_data(self, xml_data: str, api_info: APIInfo) -> List[SecurityCheckResult]:
        """分析XML数据"""
        results = []
        
        try:
            root = ET.fromstring(xml_data)
            results.extend(self._analyze_xml_element(root, api_info))
        except ET.ParseError as e:
            logger.warning(f"XML解析失败: {e}")
        except Exception as e:
            logger.error(f"分析XML数据失败: {e}")
        
        return results
    
    def _analyze_xml_element(self, element: ET.Element, api_info: APIInfo, path: str = "") -> List[SecurityCheckResult]:
        """分析XML元素"""
        results = []
        
        if len(path.split('/')) > self.max_depth:
            return results
        
        try:
            current_path = f"{path}/{element.tag}" if path else element.tag
            
            # 检查标签名是否敏感
            if self._is_sensitive_key(element.tag):
                result = self._create_sensitive_field_result(
                    element.tag, element.text, current_path, api_info
                )
                if result:
                    results.append(result)
            
            # 检查文本内容
            if element.text:
                sensitive_info = self._detect_sensitive_value(element.text)
                if sensitive_info:
                    result = self._create_sensitive_value_result(
                        element.tag, element.text, sensitive_info, current_path, api_info
                    )
                    if result:
                        results.append(result)
            
            # 检查属性
            for attr_name, attr_value in element.attrib.items():
                if self._is_sensitive_key(attr_name):
                    result = self._create_sensitive_field_result(
                        attr_name, attr_value, f"{current_path}@{attr_name}", api_info
                    )
                    if result:
                        results.append(result)
            
            # 递归检查子元素
            for child in element:
                results.extend(self._analyze_xml_element(child, api_info, current_path))
        
        except Exception as e:
            logger.error(f"分析XML元素失败: {e}")
        
        return results
    
    def _analyze_text_data(self, text: str, api_info: APIInfo) -> List[SecurityCheckResult]:
        """分析纯文本数据"""
        results = []
        
        try:
            # 检测敏感信息模式
            sensitive_matches = self._detect_sensitive_patterns(text, api_info)
            results.extend(sensitive_matches)
            
        except Exception as e:
            logger.error(f"分析文本数据失败: {e}")
        
        return results
    
    def _analyze_response_headers(self, headers: Dict[str, str], api_info: APIInfo) -> List[SecurityCheckResult]:
        """分析响应头"""
        results = []
        
        try:
            # 检查敏感头部
            sensitive_headers = ['set-cookie', 'authorization', 'x-auth-token', 'x-api-key']
            
            for header_name, header_value in headers.items():
                if header_name.lower() in sensitive_headers:
                    # 检查是否泄露敏感信息
                    if self._contains_sensitive_info(header_value):
                        result = SecurityCheckResult(
                            check_type=VulnerabilityType.FIELD_EXPOSURE,
                            risk_level=RiskLevel.MEDIUM,
                            is_vulnerable=True,
                            title="响应头包含敏感信息",
                            description=f"响应头 {header_name} 可能包含敏感信息",
                            technical_details={
                                'header_name': header_name,
                                'header_value': header_value[:100] + '...' if len(header_value) > 100 else header_value
                            },
                            evidence=[f"敏感响应头: {header_name}"],
                            recommendation="检查响应头是否需要包含敏感信息，考虑使用更安全的方式传递",
                            api_info=api_info
                        )
                        results.append(result)
        
        except Exception as e:
            logger.error(f"分析响应头失败: {e}")
        
        return results
    
    def _analyze_status_code(self, status_code: int, api_info: APIInfo) -> List[SecurityCheckResult]:
        """分析状态码"""
        results = []
        
        try:
            # 检查异常状态码
            if status_code == 500:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.OWASP_API8_SECURITY_MISCONFIGURATION,
                    risk_level=RiskLevel.LOW,
                    is_vulnerable=True,
                    title="服务器内部错误",
                    description="API返回500状态码，可能存在服务器配置问题",
                    technical_details={'status_code': status_code},
                    evidence=[f"状态码: {status_code}"],
                    recommendation="检查服务器配置和错误处理机制",
                    api_info=api_info
                )
                results.append(result)
            
            elif status_code == 403:
                # 403可能表示存在访问控制，这是好的
                pass
            
            elif status_code >= 400:
                # 其他4xx错误可能表示配置问题
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.OWASP_API8_SECURITY_MISCONFIGURATION,
                    risk_level=RiskLevel.INFO,
                    is_vulnerable=True,
                    title="客户端错误状态码",
                    description=f"API返回{status_code}状态码",
                    technical_details={'status_code': status_code},
                    evidence=[f"状态码: {status_code}"],
                    recommendation="检查API的错误处理和状态码返回逻辑",
                    api_info=api_info
                )
                results.append(result)
        
        except Exception as e:
            logger.error(f"分析状态码失败: {e}")
        
        return results
    
    def _load_sensitive_patterns(self) -> Dict[str, re.Pattern]:
        """加载敏感信息检测模式"""
        patterns = {
            'phone': re.compile(r'1[3-9]\d{9}'),  # 中国手机号
            'idcard': re.compile(r'\d{17}[\dXx]|\d{15}'),  # 身份证号
            'bankcard': re.compile(r'\d{16,19}'),  # 银行卡号
            'email': re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'),  # 邮箱
            'ip_address': re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b'),  # IP地址
            'token': re.compile(r'[a-zA-Z0-9]{32,}'),  # 可能的token
        }
        
        return patterns
    
    def _is_json(self, text: str) -> bool:
        """检查是否为JSON格式"""
        try:
            json.loads(text)
            return True
        except (json.JSONDecodeError, TypeError):
            return False
    
    def _is_xml(self, text: str) -> bool:
        """检查是否为XML格式"""
        try:
            ET.fromstring(text)
            return True
        except ET.ParseError:
            return False
    
    def _is_sensitive_key(self, key: str) -> bool:
        """检查键名是否敏感"""
        key_lower = key.lower()
        return any(keyword in key_lower for keyword in self.sensitive_keywords)
    
    def _detect_sensitive_value(self, value: str) -> List[str]:
        """检测值中的敏感信息"""
        sensitive_info = []
        
        for pattern_name, pattern in self.sensitive_patterns.items():
            if pattern.search(str(value)):
                sensitive_info.append(pattern_name)
        
        return sensitive_info
    
    def _detect_sensitive_patterns(self, text: str, api_info: APIInfo) -> List[SecurityCheckResult]:
        """使用正则表达式检测敏感模式"""
        results = []
        
        for pattern_name, pattern in self.sensitive_patterns.items():
            matches = pattern.findall(text)
            if matches:
                result = SecurityCheckResult(
                    check_type=VulnerabilityType.FIELD_EXPOSURE,
                    risk_level=self._get_risk_level_for_pattern(pattern_name),
                    is_vulnerable=True,
                    title=f"检测到{pattern_name}信息泄露",
                    description=f"响应中包含{len(matches)}个{pattern_name}",
                    technical_details={
                        'pattern_type': pattern_name,
                        'match_count': len(matches),
                        'matches': matches[:5]  # 只显示前5个匹配
                    },
                    evidence=[f"检测到{pattern_name}: {match}" for match in matches[:3]],
                    recommendation=f"检查是否需要在响应中包含{pattern_name}信息",
                    api_info=api_info
                )
                results.append(result)
        
        return results
    
    def _contains_sensitive_info(self, text: str) -> bool:
        """检查文本是否包含敏感信息"""
        return any(pattern.search(text) for pattern in self.sensitive_patterns.values())
    
    def _get_risk_level_for_pattern(self, pattern_name: str) -> RiskLevel:
        """根据模式类型获取风险等级"""
        high_risk_patterns = ['idcard', 'bankcard', 'phone']
        medium_risk_patterns = ['email', 'token']
        
        if pattern_name in high_risk_patterns:
            return RiskLevel.HIGH
        elif pattern_name in medium_risk_patterns:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _create_sensitive_field_result(
        self, 
        field_name: str, 
        field_value: Any, 
        path: str, 
        api_info: APIInfo
    ) -> Optional[SecurityCheckResult]:
        """创建敏感字段检测结果"""
        return SecurityCheckResult(
            check_type=VulnerabilityType.FIELD_EXPOSURE,
            risk_level=RiskLevel.MEDIUM,
            is_vulnerable=True,
            title="敏感字段暴露",
            description=f"响应中包含敏感字段: {field_name}",
            technical_details={
                'field_name': field_name,
                'field_path': path,
                'field_value': str(field_value)[:100] + '...' if len(str(field_value)) > 100 else str(field_value)
            },
            evidence=[f"敏感字段: {field_name} at {path}"],
            recommendation="检查是否需要在响应中包含此敏感字段",
            api_info=api_info
        )
    
    def _create_sensitive_value_result(
        self,
        field_name: str,
        field_value: str,
        sensitive_types: List[str],
        path: str,
        api_info: APIInfo
    ) -> Optional[SecurityCheckResult]:
        """创建敏感值检测结果"""
        return SecurityCheckResult(
            check_type=VulnerabilityType.FIELD_EXPOSURE,
            risk_level=RiskLevel.HIGH,
            is_vulnerable=True,
            title="敏感信息泄露",
            description=f"字段 {field_name} 包含敏感信息: {', '.join(sensitive_types)}",
            technical_details={
                'field_name': field_name,
                'field_path': path,
                'sensitive_types': sensitive_types,
                'field_value': field_value[:50] + '...' if len(field_value) > 50 else field_value
            },
            evidence=[f"敏感信息类型: {', '.join(sensitive_types)}"],
            recommendation="移除或脱敏响应中的敏感信息",
            api_info=api_info
        )
