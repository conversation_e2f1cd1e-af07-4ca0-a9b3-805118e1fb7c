"""
淘宝API安全检测Agent

基于LangGraph框架构建的专业API安全检测Agent，
专门用于检测淘宝网站及其子域名系统的API安全问题。
"""

__version__ = "1.0.0"
__author__ = "API Security Team"
__description__ = "Professional Taobao API Security Detection Agent"

from .agent.main_agent import TaobaoAPISecurityAgent
from .agent.state import AgentState, SecurityCheckResult, APIInfo

__all__ = [
    "TaobaoAPISecurityAgent",
    "AgentState", 
    "SecurityCheckResult",
    "APIInfo",
]
