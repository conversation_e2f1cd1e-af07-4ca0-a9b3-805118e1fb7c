"""
网页数据解析器

解析网页爬取数据，提取API调用信息。
"""

import re
import json
from typing import List, Dict, Any, Optional, Set
from urllib.parse import urlparse, urljoin
from datetime import datetime
import logging

from bs4 import BeautifulSoup

from ..agent.state import APIInfo
from ..utils.validators import validate_url, validate_taobao_domain

logger = logging.getLogger(__name__)


class WebDataParser:
    """网页数据解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.api_patterns = [
            # 常见的API路径模式
            r'/api/[^"\s]+',
            r'/ajax/[^"\s]+',
            r'/service/[^"\s]+',
            r'/rest/[^"\s]+',
            r'/v\d+/[^"\s]+',
            # 淘宝特定的API模式
            r'/mtop\.[^"\s]+',
            r'/h5api/[^"\s]+',
            r'/gw/[^"\s]+',
        ]
        
        self.js_api_patterns = [
            # JavaScript中的API调用模式
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'axios\.[get|post|put|delete]+\s*\(\s*["\']([^"\']+)["\']',
            r'\$\.ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest.*open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']',
        ]
    
    def parse(self, data: str, base_url: str = None) -> List[APIInfo]:
        """
        解析网页数据
        
        Args:
            data: 网页HTML内容
            base_url: 基础URL，用于解析相对路径
            
        Returns:
            解析出的API信息列表
        """
        try:
            soup = BeautifulSoup(data, 'html.parser')
            api_urls = set()
            
            # 从HTML中提取API URL
            api_urls.update(self._extract_from_html(soup, base_url))
            
            # 从JavaScript中提取API URL
            api_urls.update(self._extract_from_javascript(soup, base_url))
            
            # 从内联JSON数据中提取API URL
            api_urls.update(self._extract_from_json_data(soup, base_url))
            
            # 转换为API信息对象
            api_list = []
            for url in api_urls:
                if validate_taobao_domain(url):
                    api_info = self._create_api_info_from_url(url)
                    if api_info:
                        api_list.append(api_info)
            
            logger.info(f"从网页数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except Exception as e:
            logger.error(f"网页数据解析失败: {e}")
            return []
    
    def _extract_from_html(self, soup: BeautifulSoup, base_url: str = None) -> Set[str]:
        """
        从HTML元素中提取API URL
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL
            
        Returns:
            API URL集合
        """
        api_urls = set()
        
        # 从链接中提取
        for link in soup.find_all(['a', 'link'], href=True):
            href = link['href']
            full_url = self._resolve_url(href, base_url)
            if full_url and self._is_api_url(full_url):
                api_urls.add(full_url)
        
        # 从表单action中提取
        for form in soup.find_all('form', action=True):
            action = form['action']
            full_url = self._resolve_url(action, base_url)
            if full_url and self._is_api_url(full_url):
                api_urls.add(full_url)
        
        # 从data属性中提取
        for element in soup.find_all(attrs={'data-url': True}):
            data_url = element['data-url']
            full_url = self._resolve_url(data_url, base_url)
            if full_url and self._is_api_url(full_url):
                api_urls.add(full_url)
        
        # 从其他data-*属性中提取
        for element in soup.find_all():
            for attr_name, attr_value in element.attrs.items():
                if attr_name.startswith('data-') and isinstance(attr_value, str):
                    if self._is_api_url(attr_value):
                        full_url = self._resolve_url(attr_value, base_url)
                        if full_url:
                            api_urls.add(full_url)
        
        return api_urls
    
    def _extract_from_javascript(self, soup: BeautifulSoup, base_url: str = None) -> Set[str]:
        """
        从JavaScript代码中提取API URL
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL
            
        Returns:
            API URL集合
        """
        api_urls = set()
        
        # 获取所有script标签
        scripts = soup.find_all('script')
        
        for script in scripts:
            if script.string:
                script_content = script.string
                
                # 使用正则表达式匹配API调用
                for pattern in self.js_api_patterns:
                    matches = re.findall(pattern, script_content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]  # 取第一个捕获组
                        
                        full_url = self._resolve_url(match, base_url)
                        if full_url and self._is_api_url(full_url):
                            api_urls.add(full_url)
                
                # 查找字符串中的API路径
                for pattern in self.api_patterns:
                    matches = re.findall(pattern, script_content)
                    for match in matches:
                        full_url = self._resolve_url(match, base_url)
                        if full_url and validate_url(full_url):
                            api_urls.add(full_url)
        
        return api_urls
    
    def _extract_from_json_data(self, soup: BeautifulSoup, base_url: str = None) -> Set[str]:
        """
        从页面中的JSON数据中提取API URL
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL
            
        Returns:
            API URL集合
        """
        api_urls = set()
        
        # 查找包含JSON数据的script标签
        json_scripts = soup.find_all('script', type='application/json')
        json_scripts.extend(soup.find_all('script', type='application/ld+json'))
        
        for script in json_scripts:
            if script.string:
                try:
                    json_data = json.loads(script.string)
                    urls = self._extract_urls_from_json(json_data)
                    
                    for url in urls:
                        full_url = self._resolve_url(url, base_url)
                        if full_url and self._is_api_url(full_url):
                            api_urls.add(full_url)
                            
                except json.JSONDecodeError:
                    continue
        
        # 查找其他可能包含JSON的script标签
        for script in soup.find_all('script'):
            if script.string and 'window.' in script.string:
                # 尝试提取window对象中的JSON数据
                json_matches = re.findall(r'window\.\w+\s*=\s*({.+?});', script.string, re.DOTALL)
                for json_match in json_matches:
                    try:
                        json_data = json.loads(json_match)
                        urls = self._extract_urls_from_json(json_data)
                        
                        for url in urls:
                            full_url = self._resolve_url(url, base_url)
                            if full_url and self._is_api_url(full_url):
                                api_urls.add(full_url)
                                
                    except json.JSONDecodeError:
                        continue
        
        return api_urls
    
    def _extract_urls_from_json(self, data: Any) -> List[str]:
        """
        递归从JSON数据中提取URL
        
        Args:
            data: JSON数据
            
        Returns:
            URL列表
        """
        urls = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str) and (validate_url(value) or self._is_api_path(value)):
                    urls.append(value)
                else:
                    urls.extend(self._extract_urls_from_json(value))
        elif isinstance(data, list):
            for item in data:
                urls.extend(self._extract_urls_from_json(item))
        elif isinstance(data, str):
            if validate_url(data) or self._is_api_path(data):
                urls.append(data)
        
        return urls
    
    def _is_api_url(self, url: str) -> bool:
        """
        判断是否为API URL
        
        Args:
            url: URL字符串
            
        Returns:
            是否为API URL
        """
        if not url:
            return False
        
        url_lower = url.lower()
        
        # 检查API路径模式
        for pattern in self.api_patterns:
            if re.search(pattern, url_lower):
                return True
        
        # 检查常见的API关键词
        api_keywords = [
            '/api/', '/ajax/', '/service/', '/rest/', '/graphql',
            '/mtop.', '/h5api/', '/gw/', '/rpc/', '/jsonrpc'
        ]
        
        for keyword in api_keywords:
            if keyword in url_lower:
                return True
        
        return False
    
    def _is_api_path(self, path: str) -> bool:
        """
        判断是否为API路径（相对路径）
        
        Args:
            path: 路径字符串
            
        Returns:
            是否为API路径
        """
        if not path or not path.startswith('/'):
            return False
        
        return self._is_api_url(path)
    
    def _resolve_url(self, url: str, base_url: str = None) -> Optional[str]:
        """
        解析相对URL为绝对URL
        
        Args:
            url: 原始URL
            base_url: 基础URL
            
        Returns:
            解析后的绝对URL
        """
        if not url:
            return None
        
        # 如果已经是绝对URL，直接返回
        if url.startswith(('http://', 'https://')):
            return url
        
        # 如果有基础URL，进行拼接
        if base_url:
            try:
                return urljoin(base_url, url)
            except Exception:
                return None
        
        # 如果是相对路径且没有基础URL，尝试添加默认协议和域名
        if url.startswith('/'):
            # 假设是淘宝域名
            return f"https://www.taobao.com{url}"
        
        return None
    
    def _create_api_info_from_url(self, url: str) -> Optional[APIInfo]:
        """
        从URL创建API信息对象
        
        Args:
            url: API URL
            
        Returns:
            API信息对象
        """
        try:
            if not validate_url(url):
                return None
            
            parsed_url = urlparse(url)
            
            # 根据路径推测HTTP方法
            method = self._guess_http_method(parsed_url.path)
            
            return APIInfo(
                url=url,
                method=method,
                domain=parsed_url.netloc,
                path=parsed_url.path,
                timestamp=datetime.now(),
                source='web_scraping'
            )
            
        except Exception as e:
            logger.error(f"从URL创建API信息失败: {e}")
            return None
    
    def _guess_http_method(self, path: str) -> str:
        """
        根据路径推测HTTP方法
        
        Args:
            path: URL路径
            
        Returns:
            推测的HTTP方法
        """
        path_lower = path.lower()
        
        # 根据路径关键词推测方法
        if any(keyword in path_lower for keyword in ['create', 'add', 'insert', 'post']):
            return 'POST'
        elif any(keyword in path_lower for keyword in ['update', 'edit', 'modify', 'put']):
            return 'PUT'
        elif any(keyword in path_lower for keyword in ['delete', 'remove', 'del']):
            return 'DELETE'
        else:
            # 默认为GET方法
            return 'GET'
