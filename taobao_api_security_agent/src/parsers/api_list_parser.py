"""
API列表解析器

解析各种格式的API列表数据。
"""

import json
import csv
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
from datetime import datetime
import logging
from io import StringIO

from ..agent.state import APIInfo
from ..utils.validators import validate_url, validate_taobao_domain

logger = logging.getLogger(__name__)


class APIListParser:
    """API列表解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.supported_formats = ['json', 'csv', 'txt', 'xml']
    
    def parse(self, data: str, format_type: str = 'auto') -> List[APIInfo]:
        """
        解析API列表数据
        
        Args:
            data: API列表数据内容
            format_type: 数据格式类型
            
        Returns:
            解析出的API信息列表
        """
        if format_type == 'auto':
            format_type = self._detect_format(data)
        
        if format_type == 'json':
            return self._parse_json(data)
        elif format_type == 'csv':
            return self._parse_csv(data)
        elif format_type == 'txt':
            return self._parse_txt(data)
        elif format_type == 'xml':
            return self._parse_xml(data)
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")
    
    def _detect_format(self, data: str) -> str:
        """
        自动检测数据格式
        
        Args:
            data: 数据内容
            
        Returns:
            检测到的格式类型
        """
        data_stripped = data.strip()
        
        # 检查JSON格式
        if (data_stripped.startswith('{') and data_stripped.endswith('}')) or \
           (data_stripped.startswith('[') and data_stripped.endswith(']')):
            try:
                json.loads(data)
                return 'json'
            except json.JSONDecodeError:
                pass
        
        # 检查XML格式
        if data_stripped.startswith('<') and data_stripped.endswith('>'):
            return 'xml'
        
        # 检查CSV格式（包含逗号分隔符）
        if ',' in data and '\n' in data:
            lines = data.split('\n')
            if len(lines) > 1:
                # 检查是否有一致的列数
                first_line_cols = len(lines[0].split(','))
                if first_line_cols > 1:
                    return 'csv'
        
        # 默认为文本格式
        return 'txt'
    
    def _parse_json(self, json_data: str) -> List[APIInfo]:
        """
        解析JSON格式的API列表
        
        Args:
            json_data: JSON格式的API列表
            
        Returns:
            API信息列表
        """
        try:
            data = json.loads(json_data)
            
            if not isinstance(data, list):
                if isinstance(data, dict):
                    # 如果是单个API对象，转换为列表
                    data = [data]
                else:
                    raise ValueError("JSON数据必须是列表或对象格式")
            
            api_list = []
            
            for item in data:
                try:
                    api_info = self._create_api_info_from_dict(item)
                    if api_info and validate_taobao_domain(api_info.url):
                        api_list.append(api_info)
                except Exception as e:
                    logger.warning(f"解析API条目失败: {e}")
                    continue
            
            logger.info(f"从JSON数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise ValueError("无效的JSON格式数据")
    
    def _parse_csv(self, csv_data: str) -> List[APIInfo]:
        """
        解析CSV格式的API列表
        
        Args:
            csv_data: CSV格式的API列表
            
        Returns:
            API信息列表
        """
        try:
            csv_file = StringIO(csv_data)
            reader = csv.DictReader(csv_file)
            
            api_list = []
            
            for row in reader:
                try:
                    api_info = self._create_api_info_from_dict(row)
                    if api_info and validate_taobao_domain(api_info.url):
                        api_list.append(api_info)
                except Exception as e:
                    logger.warning(f"解析CSV行失败: {e}")
                    continue
            
            logger.info(f"从CSV数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except Exception as e:
            logger.error(f"CSV解析失败: {e}")
            raise ValueError("无效的CSV格式数据")
    
    def _parse_txt(self, txt_data: str) -> List[APIInfo]:
        """
        解析文本格式的API列表
        
        Args:
            txt_data: 文本格式的API列表
            
        Returns:
            API信息列表
        """
        lines = txt_data.strip().split('\n')
        api_list = []
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            try:
                # 尝试不同的文本格式
                api_info = self._parse_text_line(line)
                if api_info and validate_taobao_domain(api_info.url):
                    api_list.append(api_info)
            except Exception as e:
                logger.warning(f"解析文本行失败: {line}, 错误: {e}")
                continue
        
        logger.info(f"从文本数据中解析出 {len(api_list)} 个API")
        return api_list
    
    def _parse_xml(self, xml_data: str) -> List[APIInfo]:
        """
        解析XML格式的API列表
        
        Args:
            xml_data: XML格式的API列表
            
        Returns:
            API信息列表
        """
        try:
            import xml.etree.ElementTree as ET
            
            root = ET.fromstring(xml_data)
            api_list = []
            
            # 查找API元素
            for api_element in root.findall('.//api'):
                try:
                    api_dict = {}
                    
                    # 提取属性
                    api_dict.update(api_element.attrib)
                    
                    # 提取子元素
                    for child in api_element:
                        api_dict[child.tag] = child.text
                    
                    api_info = self._create_api_info_from_dict(api_dict)
                    if api_info and validate_taobao_domain(api_info.url):
                        api_list.append(api_info)
                        
                except Exception as e:
                    logger.warning(f"解析XML API元素失败: {e}")
                    continue
            
            logger.info(f"从XML数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except ET.ParseError as e:
            logger.error(f"XML解析失败: {e}")
            raise ValueError("无效的XML格式数据")
    
    def _create_api_info_from_dict(self, data: Dict[str, Any]) -> Optional[APIInfo]:
        """
        从字典数据创建API信息
        
        Args:
            data: 包含API信息的字典
            
        Returns:
            API信息对象
        """
        try:
            # 尝试不同的字段名映射
            url_fields = ['url', 'endpoint', 'api_url', 'uri']
            method_fields = ['method', 'http_method', 'verb']
            
            url = None
            method = 'GET'
            
            # 查找URL
            for field in url_fields:
                if field in data and data[field]:
                    url = str(data[field]).strip()
                    break
            
            if not url or not validate_url(url):
                return None
            
            # 查找HTTP方法
            for field in method_fields:
                if field in data and data[field]:
                    method = str(data[field]).strip().upper()
                    break
            
            parsed_url = urlparse(url)
            
            # 提取其他字段
            request_headers = self._extract_headers(data, 'request_headers', 'headers')
            response_headers = self._extract_headers(data, 'response_headers')
            
            return APIInfo(
                url=url,
                method=method,
                domain=parsed_url.netloc,
                path=parsed_url.path,
                request_headers=request_headers,
                request_body=data.get('request_body', data.get('body')),
                request_params=self._extract_params(data),
                response_status=self._safe_int(data.get('response_status', data.get('status'))),
                response_headers=response_headers,
                response_body=data.get('response_body', data.get('response')),
                response_time=self._safe_float(data.get('response_time', data.get('time'))),
                timestamp=datetime.now(),
                source='api_list'
            )
            
        except Exception as e:
            logger.error(f"从字典创建API信息失败: {e}")
            return None
    
    def _parse_text_line(self, line: str) -> Optional[APIInfo]:
        """
        解析单行文本格式的API信息
        
        Args:
            line: 文本行
            
        Returns:
            API信息对象
        """
        # 格式1: METHOD URL
        method_url_pattern = r'^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+(.+)$'
        match = re.match(method_url_pattern, line, re.IGNORECASE)
        
        if match:
            method, url = match.groups()
            url = url.strip()
            
            if validate_url(url):
                parsed_url = urlparse(url)
                return APIInfo(
                    url=url,
                    method=method.upper(),
                    domain=parsed_url.netloc,
                    path=parsed_url.path,
                    timestamp=datetime.now(),
                    source='text'
                )
        
        # 格式2: 只有URL（默认GET方法）
        if validate_url(line):
            parsed_url = urlparse(line)
            return APIInfo(
                url=line,
                method='GET',
                domain=parsed_url.netloc,
                path=parsed_url.path,
                timestamp=datetime.now(),
                source='text'
            )
        
        return None
    
    def _extract_headers(self, data: Dict[str, Any], *field_names: str) -> Dict[str, str]:
        """提取头部信息"""
        for field_name in field_names:
            if field_name in data:
                headers_data = data[field_name]
                
                if isinstance(headers_data, dict):
                    return {str(k): str(v) for k, v in headers_data.items()}
                elif isinstance(headers_data, str):
                    try:
                        return json.loads(headers_data)
                    except json.JSONDecodeError:
                        pass
        
        return {}
    
    def _extract_params(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取请求参数"""
        params_fields = ['request_params', 'params', 'parameters', 'query_params']
        
        for field_name in params_fields:
            if field_name in data:
                params_data = data[field_name]
                
                if isinstance(params_data, dict):
                    return params_data
                elif isinstance(params_data, str):
                    try:
                        return json.loads(params_data)
                    except json.JSONDecodeError:
                        pass
        
        return {}
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
