"""
流量数据解析器

解析HAR文件、PCAP文件等网络流量数据。
"""

import json
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from datetime import datetime
import logging

from ..agent.state import APIInfo, InputType
from ..utils.validators import validate_url, validate_taobao_domain

logger = logging.getLogger(__name__)


class TrafficDataParser:
    """流量数据解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.supported_formats = ['har', 'pcap', 'json']
    
    def parse(self, data: str, format_type: str = 'auto') -> List[APIInfo]:
        """
        解析流量数据
        
        Args:
            data: 流量数据内容
            format_type: 数据格式类型
            
        Returns:
            解析出的API信息列表
        """
        if format_type == 'auto':
            format_type = self._detect_format(data)
        
        if format_type == 'har':
            return self._parse_har(data)
        elif format_type == 'pcap':
            return self._parse_pcap(data)
        elif format_type == 'json':
            return self._parse_json_traffic(data)
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")
    
    def _detect_format(self, data: str) -> str:
        """
        自动检测数据格式
        
        Args:
            data: 数据内容
            
        Returns:
            检测到的格式类型
        """
        try:
            # 尝试解析为JSON
            json_data = json.loads(data)
            
            # 检查是否为HAR格式
            if 'log' in json_data and 'entries' in json_data.get('log', {}):
                return 'har'
            
            # 检查是否为自定义JSON流量格式
            if isinstance(json_data, list) and len(json_data) > 0:
                first_item = json_data[0]
                if 'url' in first_item and 'method' in first_item:
                    return 'json'
            
            return 'json'
            
        except json.JSONDecodeError:
            # 如果不是JSON，可能是PCAP或其他格式
            if data.startswith(b'\xd4\xc3\xb2\xa1') or data.startswith(b'\xa1\xb2\xc3\xd4'):
                return 'pcap'
            
            return 'unknown'
    
    def _parse_har(self, har_data: str) -> List[APIInfo]:
        """
        解析HAR格式数据
        
        Args:
            har_data: HAR格式的JSON字符串
            
        Returns:
            API信息列表
        """
        try:
            har_json = json.loads(har_data)
            entries = har_json.get('log', {}).get('entries', [])
            
            api_list = []
            
            for entry in entries:
                try:
                    request = entry.get('request', {})
                    response = entry.get('response', {})
                    
                    url = request.get('url', '')
                    
                    # 只处理淘宝相关域名
                    if not validate_taobao_domain(url):
                        continue
                    
                    # 过滤静态资源
                    if self._is_static_resource(url):
                        continue
                    
                    api_info = self._create_api_info_from_har_entry(entry)
                    if api_info:
                        api_list.append(api_info)
                        
                except Exception as e:
                    logger.warning(f"解析HAR条目失败: {e}")
                    continue
            
            logger.info(f"从HAR数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except json.JSONDecodeError as e:
            logger.error(f"HAR数据JSON解析失败: {e}")
            raise ValueError("无效的HAR格式数据")
    
    def _parse_pcap(self, pcap_data: str) -> List[APIInfo]:
        """
        解析PCAP格式数据
        
        Args:
            pcap_data: PCAP数据
            
        Returns:
            API信息列表
        """
        # 注意：这里需要使用专门的PCAP解析库如scapy
        # 由于复杂性，这里提供一个简化的实现框架
        
        logger.warning("PCAP解析功能需要安装scapy库")
        
        try:
            # 这里应该使用scapy来解析PCAP文件
            # from scapy.all import rdpcap, TCP, Raw
            # packets = rdpcap(pcap_file)
            # 然后提取HTTP请求和响应
            
            # 临时返回空列表
            return []
            
        except Exception as e:
            logger.error(f"PCAP解析失败: {e}")
            return []
    
    def _parse_json_traffic(self, json_data: str) -> List[APIInfo]:
        """
        解析JSON格式的流量数据
        
        Args:
            json_data: JSON格式的流量数据
            
        Returns:
            API信息列表
        """
        try:
            data = json.loads(json_data)
            
            if not isinstance(data, list):
                data = [data]
            
            api_list = []
            
            for item in data:
                try:
                    api_info = self._create_api_info_from_json(item)
                    if api_info and validate_taobao_domain(api_info.url):
                        api_list.append(api_info)
                except Exception as e:
                    logger.warning(f"解析JSON流量条目失败: {e}")
                    continue
            
            logger.info(f"从JSON数据中解析出 {len(api_list)} 个API")
            return api_list
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON流量数据解析失败: {e}")
            raise ValueError("无效的JSON格式数据")
    
    def _create_api_info_from_har_entry(self, entry: Dict[str, Any]) -> Optional[APIInfo]:
        """从HAR条目创建API信息"""
        try:
            request = entry.get('request', {})
            response = entry.get('response', {})
            
            url = request.get('url', '')
            method = request.get('method', 'GET')
            
            if not validate_url(url):
                return None
            
            parsed_url = urlparse(url)
            
            # 提取请求头
            request_headers = {}
            for header in request.get('headers', []):
                request_headers[header.get('name', '')] = header.get('value', '')
            
            # 提取响应头
            response_headers = {}
            for header in response.get('headers', []):
                response_headers[header.get('name', '')] = header.get('value', '')
            
            # 提取请求参数
            request_params = {}
            if parsed_url.query:
                request_params.update(parse_qs(parsed_url.query))
            
            # POST数据
            post_data = request.get('postData', {})
            request_body = post_data.get('text', '') if post_data else None
            
            # 响应数据
            response_content = response.get('content', {})
            response_body = response_content.get('text', '') if response_content else None
            
            # 时间信息
            started_datetime = entry.get('startedDateTime', '')
            timestamp = datetime.fromisoformat(started_datetime.replace('Z', '+00:00')) if started_datetime else datetime.now()
            
            return APIInfo(
                url=url,
                method=method.upper(),
                domain=parsed_url.netloc,
                path=parsed_url.path,
                request_headers=request_headers,
                request_body=request_body,
                request_params=request_params,
                response_status=response.get('status'),
                response_headers=response_headers,
                response_body=response_body,
                response_time=response.get('time'),
                timestamp=timestamp,
                source='har'
            )
            
        except Exception as e:
            logger.error(f"创建API信息失败: {e}")
            return None
    
    def _create_api_info_from_json(self, item: Dict[str, Any]) -> Optional[APIInfo]:
        """从JSON数据创建API信息"""
        try:
            url = item.get('url', '')
            method = item.get('method', 'GET')
            
            if not validate_url(url):
                return None
            
            parsed_url = urlparse(url)
            
            return APIInfo(
                url=url,
                method=method.upper(),
                domain=parsed_url.netloc,
                path=parsed_url.path,
                request_headers=item.get('request_headers', {}),
                request_body=item.get('request_body'),
                request_params=item.get('request_params', {}),
                response_status=item.get('response_status'),
                response_headers=item.get('response_headers', {}),
                response_body=item.get('response_body'),
                response_time=item.get('response_time'),
                timestamp=datetime.now(),
                source='json'
            )
            
        except Exception as e:
            logger.error(f"从JSON创建API信息失败: {e}")
            return None
    
    def _is_static_resource(self, url: str) -> bool:
        """
        判断是否为静态资源
        
        Args:
            url: URL地址
            
        Returns:
            是否为静态资源
        """
        static_extensions = [
            '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
            '.woff', '.woff2', '.ttf', '.eot', '.mp4', '.mp3', '.pdf'
        ]
        
        static_paths = [
            '/static/', '/assets/', '/images/', '/css/', '/js/',
            '/fonts/', '/media/', '/uploads/'
        ]
        
        url_lower = url.lower()
        
        # 检查文件扩展名
        for ext in static_extensions:
            if url_lower.endswith(ext):
                return True
        
        # 检查路径
        for path in static_paths:
            if path in url_lower:
                return True
        
        return False
