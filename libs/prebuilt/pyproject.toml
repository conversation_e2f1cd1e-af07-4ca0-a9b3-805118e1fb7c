[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-prebuilt"
version = "0.6.1"
description = "Library with high-level APIs for creating and executing LangGraph agents and tools."
authors = []
requires-python = ">=3.9"
readme = "README.md"
license = "MIT"
license-files = ['LICENSE']
dependencies = [
    "langgraph-checkpoint>=2.1.0,<3.0.0",
    "langchain-core>=0.3.67",
]

[project.urls]
Repository = "https://www.github.com/langchain-ai/langgraph"

[dependency-groups]
dev = [
    "ruff",
    "codespell",
    "pytest",
    "pytest-asyncio",
    "pytest-mock",
    "pytest-watcher",
    "mypy",
    "langgraph",
    "langgraph-checkpoint",
    "langgraph-checkpoint-sqlite",
    "langgraph-checkpoint-postgres",
    "syrupy",
]

[tool.uv]
default-groups = ['dev']

[tool.uv.sources]
langgraph = { path = "../langgraph", editable = true }
langgraph-checkpoint = { path = "../checkpoint", editable = true }
langgraph-checkpoint-sqlite = { path = "../checkpoint-sqlite", editable = true }
langgraph-checkpoint-postgres = { path = "../checkpoint-postgres", editable = true }

[tool.hatch.build.targets.wheel]
include = ["langgraph"]

[tool.pytest.ini_options]
addopts = "--strict-markers --strict-config --durations=5 -vv"
asyncio_mode = "auto"

[tool.ruff]
lint.select = [ "E", "F", "I", "TID251" ]
lint.ignore = [ "E501" ]

[tool.pytest-watcher]
now = true
delay = 0.1
runner_args = ["--ff", "-v", "--tb", "short"]
patterns = ["*.py"]

[tool.mypy]
# https://mypy.readthedocs.io/en/stable/config_file.html
disallow_untyped_defs = "True"
explicit_package_bases = "True"
warn_no_return = "False"
warn_unused_ignores = "True"
warn_redundant_casts = "True"
allow_redefinition = "True"
disable_error_code = "typeddict-item, return-value"
